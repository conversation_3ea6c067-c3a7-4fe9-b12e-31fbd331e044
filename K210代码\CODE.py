
from machine import Timer,PWM
import time
from math import sqrt, pi
import math
import time
from machine import UART,Timer
from fpioa_manager import fm
import lcd,image,utime
import sensor, image, time, lcd

sensor.reset()
sensor.set_pixformat(sensor.GRAYSCALE) # 灰度更快sensor.RGB565
sensor.set_framesize(sensor.QQVGA)
sensor.set_brightness(-2)
sensor.set_saturation(-2)
sensor.skip_frames(time = 2000)

lcd.init() #初始化LCD
lcd.clear(lcd.WHITE) #清屏白色
clock = time.clock()
ZX=70
xin=70
ZY=70
yin=70
P=0
P2=0
I=0
I2=0
PI=0
PI2=0
state=0
text2=0
text2_last=0
condition=1
black=(150,255)
if condition:
    def func_servo(id0,posit0,interval0):
            ZT1=0xFF
            ZT2=0xFF
            DATA1=0X2A
            DATA2=(posit0>>8)&0xff
            DATA3=posit0&0xff
            DATA4=(interval0>>8)&0xff
            DATA5=interval0&0xff  # 条件满足时执行的代码
            data_length=0x07
            WriteDATA=0X03
            GetChecksum=(~(id0+data_length+WriteDATA+DATA1+DATA2+DATA3+DATA4+DATA5))& 0xff
            text=bytes([ZT1,ZT2,id0,data_length,WriteDATA,DATA1,DATA2,DATA3,DATA4,DATA5,GetChecksum])
            uart.write(text) #数据回传

    intiposit=2096
    posit=2025
    positx=2048
    interval=1000
    ID1=0x01
    ID2=0x02
    theta=0

    #映射串口引脚
    fm.register(24, fm.fpioa.UART1_RX, force=True)
    fm.register(7, fm.fpioa.UART1_TX, force=True)

    #初始化串口
    uart = UART(UART.UART1, 115200, read_buf_len=4096)
    #uart.write('Hello 01Studio!')
    func_servo(ID1,int(positx),interval)
    func_servo(ID2,int(posit),interval)
    time.sleep(1)
    while True:
        img = sensor.snapshot()

        text=uart.read(1) #读取数据
        if text:

             text2=text.decode('utf-8')

             #print(text2) #REPL打印
    # 下面的`threshold`应设置为足够高的值，以滤除在图像中检测到的具有
    # 低边缘幅度的噪声矩形。最适用与背景形成鲜明对比的矩形。
        img.laplacian(1, sharpen=True)

        for r in img.find_rects(threshold = 30000):
            img.draw_rectangle(r.rect(), color = (255, 0, 0))
            for p in r.corners(): img.draw_circle(p[0], p[1], 5, color = (0, 255, 0))

            cor=r.corners()
            #print(cor[3][0])
            #print(r.corners())
            ZX=(cor[0][0]+ cor[1][0]+ cor[2][0]+  cor[3][0] )/4
            ZY=(cor[0][1]+ cor[1][1]+ cor[2][1]+  cor[3][1] )/4
        P=(ZX-xin )*0
        P2=(ZY-yin)*0
        I=(ZX-xin )*1+I
        I2=(ZY-yin)*1+I2
        PI=P+I
        PI2=P2+I2
        #print(P)
    #print("FPS %f" % clock.fps())
        #text=uart.read() #读取数据
        #print(text.decode('hex'))

        theta=theta+0.001
        if theta>=2*pi:
            theta=0

        r=pi*40
        if text2 != text2_last:
            state=1
        text2_last=text2
        if state==1:
            if text2=='2':
                positx=2048
                PI=0
                PI2=0
                func_servo(ID1,int(positx-PI),interval)
                func_servo(ID2,int(posit-PI2),interval)
                time.sleep(1)
                state=0
            if text2=='3':
                positx=1000
                PI=0
                PI2=0
                func_servo(ID1,int(positx-PI),interval)
                func_servo(ID2,int(posit-PI2),interval)
                time.sleep(1)
                state=0
            if text2=='4':
                positx=2048
                PI=0
                PI2=0
                func_servo(ID1,int(positx-PI),interval)
                func_servo(ID2,int(posit-PI2),interval)
                time.sleep(1)
                state=0
            if text2=='5':
                positx=3000
                PI=0
                PI2=0
                func_servo(ID1,int(positx-PI),interval)
                func_servo(ID2,int(posit-PI2),interval)
                time.sleep(1)
                state=0
        #print(positx)
        func_servo(ID1,int(positx-PI),interval)
        func_servo(ID2,int(posit-PI2),interval)
        #print(x)
        #print(y)
        #text=bytes([ZT1,ZT2,ID1,data_length,WriteDATA,DATA1,DATA2,DATA3,DATA4,DATA5,GetChecksum])
        #uart.write(text) #数据回传


        lcd.display(img)
#############################################################################################################
#############################################################################################################
#############################################################################################################
else:
    def func_servo(servo,angle):
        servo.duty((angle+90)/180*10+2.5)

    tim = Timer(Timer.TIMER0, Timer.CHANNEL0, mode=Timer.MODE_PWM)
    S1 = PWM(tim, freq=50, duty=0, pin=8)
    tim2 = Timer(Timer.TIMER0, Timer.CHANNEL1, mode=Timer.MODE_PWM)
    S2 = PWM(tim2, freq=50, duty=0, pin=9)
    theta=0
    intheta=0
    func_servo(S1,0)
    func_servo(S2,0)
    time.sleep(3)
    while True:

        theta=theta+0.0001
        if theta>=2*pi:
            theta=0

        r=pi*3
        x=r*math.cos(theta)
        y=r*math.sin(theta)
        func_servo(S1,int(intheta+x))
        func_servo(S2,int(intheta+y))
        print(x)
        print(y)





