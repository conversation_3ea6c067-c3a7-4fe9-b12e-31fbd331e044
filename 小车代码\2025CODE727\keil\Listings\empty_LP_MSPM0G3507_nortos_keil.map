Component: Arm Compiler for Embedded 6.22 Tool: armlink [5ee90200]

==============================================================================

Section Cross References

    empty.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    empty.o(.text.main) refers to bsp_systick.o(.text.delay_ms) for delay_ms
    empty.o(.text.main) refers to bsp_oled.o(.text.OLED_Clear) for OLED_Clear
    empty.o(.text.main) refers to bsp_oled.o(.text.OLED_ShowString) for OLED_ShowString
    empty.o(.text.main) refers to mpu6050.o(.text.MPU6050_initialize) for MPU6050_initialize
    empty.o(.text.main) refers to mpu6050.o(.text.DMP_Init) for DMP_Init
    empty.o(.text.main) refers to empty.o(.text.xunji) for xunji
    empty.o(.text.main) refers to fadd.o(.text) for __aeabi_fadd
    empty.o(.text.main) refers to ffixi.o(.text) for __aeabi_f2iz
    empty.o(.text.main) refers to bsp_systick.o(.text.Systick_getTick) for Systick_getTick
    empty.o(.text.main) refers to mpu6050.o(.text.Read_DMP) for Read_DMP
    empty.o(.text.main) refers to fflti.o(.text) for __aeabi_i2f
    empty.o(.text.main) refers to show.o(.text.oled_show) for oled_show
    empty.o(.text.main) refers to bsp_oled.o(.data.UserOLED) for UserOLED
    empty.o(.text.main) refers to empty.o(.rodata.str1.1) for [Anonymous Symbol]
    empty.o(.text.main) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    empty.o(.text.main) refers to balance.o(.bss.Velocity1) for Velocity1
    empty.o(.text.main) refers to balance.o(.bss.Turn1) for Turn1
    empty.o(.text.main) refers to empty.o(.data.xuanti) for xuanti
    empty.o(.text.main) refers to balance.o(.data.Flag_Stop) for Flag_Stop
    empty.o(.text.main) refers to empty.o(.bss.state6050) for state6050
    empty.o(.text.main) refers to empty.o(.bss.time) for time
    empty.o(.text.main) refers to mpu6050.o(.bss.mpu6050) for mpu6050
    empty.o(.text.main) refers to empty.o(.bss.m6050init) for m6050init
    empty.o(.text.main) refers to empty.o(.bss.HD6) for HD6
    empty.o(.text.main) refers to empty.o(.data.KEY0) for KEY0
    empty.o(.text.main) refers to mpu6050.o(.bss.Roll) for Roll
    empty.o(.text.main) refers to mpu6050.o(.bss.Pitch) for Pitch
    empty.o(.text.main) refers to mpu6050.o(.bss.Yaw) for Yaw
    empty.o(.text.main) refers to mpu6050.o(.bss.gyro) for gyro
    empty.o(.text.main) refers to mpu6050.o(.bss.accel) for accel
    empty.o(.text.main) refers to empty.o(.bss.main.hd6_debounce) for [Anonymous Symbol]
    empty.o(.text.main) refers to empty.o(.bss.main.key0_debounce) for [Anonymous Symbol]
    empty.o(.text.main) refers to empty.o(.bss.gy) for gy
    empty.o(.ARM.exidx.text.main) refers to empty.o(.text.main) for [Anonymous Symbol]
    empty.o(.text.xunji) refers to fadd.o(.text) for __aeabi_fadd
    empty.o(.text.xunji) refers to balance.o(.data.Flag_Stop) for Flag_Stop
    empty.o(.text.xunji) refers to empty.o(.bss.HD5) for HD5
    empty.o(.text.xunji) refers to empty.o(.bss.HD0) for HD0
    empty.o(.text.xunji) refers to empty.o(.bss.HD1) for HD1
    empty.o(.text.xunji) refers to empty.o(.bss.HD2) for HD2
    empty.o(.text.xunji) refers to empty.o(.bss.HD3) for HD3
    empty.o(.text.xunji) refers to empty.o(.bss.HD4) for HD4
    empty.o(.text.xunji) refers to empty.o(.bss.gyst) for gyst
    empty.o(.text.xunji) refers to empty.o(.bss.gy) for gy
    empty.o(.text.xunji) refers to balance.o(.bss.Turn1) for Turn1
    empty.o(.ARM.exidx.text.xunji) refers to empty.o(.text.xunji) for [Anonymous Symbol]
    empty.o(.text.GROUP1_IRQHandler) refers to mpu6050.o(.text.Read_DMP) for Read_DMP
    empty.o(.text.GROUP1_IRQHandler) refers to fflti.o(.text) for __aeabi_i2f
    empty.o(.text.GROUP1_IRQHandler) refers to balance.o(.text.BalanceControlTask) for BalanceControlTask
    empty.o(.text.GROUP1_IRQHandler) refers to empty.o(.bss.g_EncoderACount) for g_EncoderACount
    empty.o(.text.GROUP1_IRQHandler) refers to empty.o(.bss.g_EncoderBCount) for g_EncoderBCount
    empty.o(.text.GROUP1_IRQHandler) refers to empty.o(.data.mainTaskFreqCheck) for mainTaskFreqCheck
    empty.o(.text.GROUP1_IRQHandler) refers to empty.o(.bss.mainUseTimePriv) for mainUseTimePriv
    empty.o(.text.GROUP1_IRQHandler) refers to mpu6050.o(.bss.Roll) for Roll
    empty.o(.text.GROUP1_IRQHandler) refers to mpu6050.o(.bss.mpu6050) for mpu6050
    empty.o(.text.GROUP1_IRQHandler) refers to mpu6050.o(.bss.Pitch) for Pitch
    empty.o(.text.GROUP1_IRQHandler) refers to mpu6050.o(.bss.Yaw) for Yaw
    empty.o(.text.GROUP1_IRQHandler) refers to mpu6050.o(.bss.gyro) for gyro
    empty.o(.text.GROUP1_IRQHandler) refers to mpu6050.o(.bss.accel) for accel
    empty.o(.text.GROUP1_IRQHandler) refers to empty.o(.bss.mainFreqPriv) for mainFreqPriv
    empty.o(.text.GROUP1_IRQHandler) refers to empty.o(.bss.mainTaskFreq) for mainTaskFreq
    empty.o(.text.GROUP1_IRQHandler) refers to empty.o(.bss.mainTaskUseTime) for mainTaskUseTime
    empty.o(.ARM.exidx.text.GROUP1_IRQHandler) refers to empty.o(.text.GROUP1_IRQHandler) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.UART0_IRQHandler) refers to empty.o(.text.UART0_IRQHandler) for [Anonymous Symbol]
    empty.o(.text.K210_Receive_Data) refers to memseta.o(.text) for __aeabi_memclr4
    empty.o(.text.K210_Receive_Data) refers to empty.o(.bss.state) for state
    empty.o(.text.K210_Receive_Data) refers to empty.o(.bss.openmv) for openmv
    empty.o(.text.K210_Receive_Data) refers to empty.o(.bss.x) for x
    empty.o(.text.K210_Receive_Data) refers to empty.o(.bss.y) for y
    empty.o(.ARM.exidx.text.K210_Receive_Data) refers to empty.o(.text.K210_Receive_Data) for [Anonymous Symbol]
    empty.o(.text.UART2_IRQHandler) refers to empty.o(.text.K210_Receive_Data) for K210_Receive_Data
    empty.o(.text.UART2_IRQHandler) refers to empty.o(.bss.data1) for data1
    empty.o(.ARM.exidx.text.UART2_IRQHandler) refers to empty.o(.text.UART2_IRQHandler) for [Anonymous Symbol]
    empty.o(.data.mainTaskFreqCheck) refers to bsp_debugtimer.o(.data.RTOSTaskDebug) for RTOSTaskDebug
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to empty.o(.text.GROUP1_IRQHandler) for GROUP1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to balance.o(.text.ADC1_IRQHandler) for ADC1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to empty.o(.text.UART2_IRQHandler) for UART2_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to empty.o(.text.UART0_IRQHandler) for UART0_IRQHandler
    startup_mspm0g350x_uvision.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) for SYSCFG_DL_PWM_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for SYSCFG_DL_TIMER_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DebugTimer_init) for SYSCFG_DL_DebugTimer_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) for SYSCFG_DL_I2C_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for SYSCFG_DL_UART_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) for SYSCFG_DL_UART_2_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init) for SYSCFG_DL_ADC12_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for SYSCFG_DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gDebugTimerBackup) for gDebugTimerBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for DL_SYSCTL_configSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.rodata.gSYSPLLConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_TimerA_initPWMMode) for DL_TimerA_initPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.rodata.gPWM_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_DebugTimer_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_DebugTimer_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_DebugTimer_init) refers to ti_msp_dl_config.o(.rodata.gDebugTimerClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_DebugTimer_init) refers to ti_msp_dl_config.o(.rodata.gDebugTimerTimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DebugTimer_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DebugTimer_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for DL_I2C_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.rodata.gUART_2ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.rodata.gUART_2Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for DL_ADC12_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init) refers to ti_msp_dl_config.o(.rodata.gADC12_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ADC12_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for DL_TimerA_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for DL_Timer_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gDebugTimerBackup) for gDebugTimerBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for DL_TimerA_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for DL_Timer_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gDebugTimerBackup) for gDebugTimerBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_configReference) refers to dl_vref.o(.text.DL_VREF_configReference) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_setClockConfig) refers to dl_vref.o(.text.DL_VREF_setClockConfig) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_getClockConfig) refers to dl_vref.o(.text.DL_VREF_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    dl_trng.o(.ARM.exidx.text.DL_TRNG_saveConfiguration) refers to dl_trng.o(.text.DL_TRNG_saveConfiguration) for [Anonymous Symbol]
    dl_trng.o(.ARM.exidx.text.DL_TRNG_restoreConfiguration) refers to dl_trng.o(.text.DL_TRNG_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig) refers to dl_timer.o(.text.DL_Timer_getClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_initCaptureMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCompareTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_initPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Timer_overrideCCPOut) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled) refers to dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_Timer_initPWMMode) for DL_Timer_initPWMMode
    dl_timer.o(.ARM.exidx.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_TimerA_initPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_setFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_getFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_configQEIHallInputMode) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_init) refers to dl_spi.o(.text.DL_SPI_init) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig) refers to dl_spi.o(.text.DL_SPI_setClockConfig) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig) refers to dl_spi.o(.text.DL_SPI_getClockConfig) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration) refers to dl_spi.o(.text.DL_SPI_saveConfiguration) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration) refers to dl_spi.o(.text.DL_SPI_restoreConfiguration) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO32) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) for [Anonymous Symbol]
    dl_opa.o(.ARM.exidx.text.DL_OPA_increaseGain) refers to dl_opa.o(.text.DL_OPA_increaseGain) for [Anonymous Symbol]
    dl_opa.o(.ARM.exidx.text.DL_OPA_decreaseGain) refers to dl_opa.o(.text.DL_OPA_decreaseGain) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isReady) refers to dl_mcan.o(.text.DL_MCAN_isReady) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setClockConfig) refers to dl_mcan.o(.text.DL_MCAN_setClockConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockConfig) refers to dl_mcan.o(.text.DL_MCAN_getClockConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isInReset) refers to dl_mcan.o(.text.DL_MCAN_isInReset) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isFDOpEnable) refers to dl_mcan.o(.text.DL_MCAN_isFDOpEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isMemInitDone) refers to dl_mcan.o(.text.DL_MCAN_isMemInitDone) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setOpMode) refers to dl_mcan.o(.text.DL_MCAN_setOpMode) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getOpMode) refers to dl_mcan.o(.text.DL_MCAN_getOpMode) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_init) refers to dl_mcan.o(.text.DL_MCAN_init) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_config) refers to dl_mcan.o(.text.DL_MCAN_config) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccConfig) refers to dl_mcan.o(.text.DL_MCAN_eccConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setBitTime) refers to dl_mcan.o(.text.DL_MCAN_setBitTime) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_msgRAMConfig) refers to dl_mcan.o(.text.DL_MCAN_msgRAMConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setExtIDAndMask) refers to dl_mcan.o(.text.DL_MCAN_setExtIDAndMask) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.rodata.cst32) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.text.DL_MCAN_writeMsgRam) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufAddReq) refers to dl_mcan.o(.text.DL_MCAN_TXBufAddReq) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getNewDataStatus) refers to dl_mcan.o(.text.DL_MCAN_getNewDataStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearNewDataStatus) refers to dl_mcan.o(.text.DL_MCAN_clearNewDataStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.rodata.cst32) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.text.DL_MCAN_readMsgRam) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_readTxEventFIFO) refers to dl_mcan.o(.text.DL_MCAN_readTxEventFIFO) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addStdMsgIDFilter) refers to dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addExtMsgIDFilter) refers to dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_lpbkModeEnable) refers to dl_mcan.o(.text.DL_MCAN_lpbkModeEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getErrCounters) refers to dl_mcan.o(.text.DL_MCAN_getErrCounters) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getProtocolStatus) refers to dl_mcan.o(.text.DL_MCAN_getProtocolStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntr) refers to dl_mcan.o(.text.DL_MCAN_enableIntr) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_selectIntrLine) refers to dl_mcan.o(.text.DL_MCAN_selectIntrLine) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrLineSelectStatus) refers to dl_mcan.o(.text.DL_MCAN_getIntrLineSelectStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntrLine) refers to dl_mcan.o(.text.DL_MCAN_enableIntrLine) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_getIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_clearIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getHighPriorityMsgStatus) refers to dl_mcan.o(.text.DL_MCAN_getHighPriorityMsgStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxFIFOStatus) refers to dl_mcan.o(.text.DL_MCAN_getRxFIFOStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeRxFIFOAck) refers to dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxFIFOQueStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxFIFOQueStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufReqPend) refers to dl_mcan.o(.text.DL_MCAN_getTxBufReqPend) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationReq) refers to dl_mcan.o(.text.DL_MCAN_txBufCancellationReq) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufTransmissionStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxBufTransmissionStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationStatus) refers to dl_mcan.o(.text.DL_MCAN_txBufCancellationStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufTransIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufCancellationIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxEventFIFOStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxEventFIFOStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addClockStopRequest) refers to dl_mcan.o(.text.DL_MCAN_addClockStopRequest) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeTxEventFIFOAck) refers to dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccForceError) refers to dl_mcan.o(.text.DL_MCAN_eccForceError) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWriteEOI) refers to dl_mcan.o(.text.DL_MCAN_eccWriteEOI) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccEnableIntr) refers to dl_mcan.o(.text.DL_MCAN_eccEnableIntr) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_eccGetIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_eccClearIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterConfig) refers to dl_mcan.o(.text.DL_MCAN_extTSCounterConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterEnable) refers to dl_mcan.o(.text.DL_MCAN_extTSCounterEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSEnableIntr) refers to dl_mcan.o(.text.DL_MCAN_extTSEnableIntr) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSWriteEOI) refers to dl_mcan.o(.text.DL_MCAN_extTSWriteEOI) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSGetUnservicedIntrCount) refers to dl_mcan.o(.text.DL_MCAN_extTSGetUnservicedIntrCount) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRevisionId) refers to dl_mcan.o(.text.DL_MCAN_getRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockStopAck) refers to dl_mcan.o(.text.DL_MCAN_getClockStopAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSSetRawStatus) refers to dl_mcan.o(.text.DL_MCAN_extTSSetRawStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSClearRawStatus) refers to dl_mcan.o(.text.DL_MCAN_extTSClearRawStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxPinState) refers to dl_mcan.o(.text.DL_MCAN_getRxPinState) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setTxPinState) refers to dl_mcan.o(.text.DL_MCAN_setTxPinState) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxPinState) refers to dl_mcan.o(.text.DL_MCAN_getTxPinState) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTSCounterVal) refers to dl_mcan.o(.text.DL_MCAN_getTSCounterVal) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClkStopAck) refers to dl_mcan.o(.text.DL_MCAN_getClkStopAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getBitTime) refers to dl_mcan.o(.text.DL_MCAN_getBitTime) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_resetTSCounter) refers to dl_mcan.o(.text.DL_MCAN_resetTSCounter) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTOCounterVal) refers to dl_mcan.o(.text.DL_MCAN_getTOCounterVal) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccAggrGetRevisionId) refers to dl_mcan.o(.text.DL_MCAN_eccAggrGetRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWrapGetRevisionId) refers to dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSIsIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_extTSIsIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getEndianVal) refers to dl_mcan.o(.text.DL_MCAN_getEndianVal) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getExtIDANDMask) refers to dl_mcan.o(.text.DL_MCAN_getExtIDANDMask) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_saveConfiguration) refers to dl_mcan.o(.text.DL_MCAN_saveConfiguration) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_restoreConfiguration) refers to dl_mcan.o(.text.DL_MCAN_restoreConfiguration) for [Anonymous Symbol]
    dl_mathacl.o(.ARM.exidx.text.DL_MathACL_configOperation) refers to dl_mathacl.o(.text.DL_MathACL_configOperation) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig) refers to dl_i2c.o(.text.DL_I2C_getClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.ramfunc) refers to dl_flashctl.o(.ramfunc) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_massErase) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectNonMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBank) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBankFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryReset) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectNonMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) for DL_FlashCTL_massEraseMultiBank
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectDataMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectDataMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectAllMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectAllMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectAllMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectAllMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_protectSector) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_protectSector) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerify) refers to dl_flashctl.o(.text.DL_FlashCTL_blankVerify) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerifyFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM) for [Anonymous Symbol]
    dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel) refers to dl_dma.o(.text.DL_DMA_initChannel) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_init) refers to dl_dac12.o(.text.DL_DAC12_init) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking8) refers to dl_dac12.o(.text.DL_DAC12_outputBlocking8) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking12) refers to dl_dac12.o(.text.DL_DAC12_outputBlocking12) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO8) refers to dl_dac12.o(.text.DL_DAC12_fillFIFO8) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO12) refers to dl_dac12.o(.text.DL_DAC12_fillFIFO12) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_performSelfCalibrationBlocking) refers to dl_dac12.o(.text.DL_DAC12_performSelfCalibrationBlocking) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock32) refers to dl_crc.o(.text.DL_CRC_calculateBlock32) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange32) refers to dl_crc.o(.text.DL_CRC_calculateMemoryRange32) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock16) refers to dl_crc.o(.text.DL_CRC_calculateBlock16) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange16) refers to dl_crc.o(.text.DL_CRC_calculateMemoryRange16) for [Anonymous Symbol]
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_setKey) refers to dl_aes.o(.text.DL_AES_setKey) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_setKeyAligned) refers to dl_aes.o(.text.DL_AES_setKeyAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadDataIn) refers to dl_aes.o(.text.DL_AES_loadDataIn) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadDataInAligned) refers to dl_aes.o(.text.DL_AES_loadDataInAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_getDataOut) refers to dl_aes.o(.text.DL_AES_getDataOut) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_getDataOutAligned) refers to dl_aes.o(.text.DL_AES_getDataOutAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataIn) refers to dl_aes.o(.text.DL_AES_loadXORDataIn) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInAligned) refers to dl_aes.o(.text.DL_AES_loadXORDataInAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTrigger) refers to dl_aes.o(.text.DL_AES_loadXORDataInWithoutTrigger) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTriggerAligned) refers to dl_aes.o(.text.DL_AES_loadXORDataInWithoutTriggerAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_xorData) refers to dl_aes.o(.text.DL_AES_xorData) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_xorDataAligned) refers to dl_aes.o(.text.DL_AES_xorDataAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_saveConfiguration) refers to dl_aes.o(.text.DL_AES_saveConfiguration) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_restoreConfiguration) refers to dl_aes.o(.text.DL_AES_restoreConfiguration) for [Anonymous Symbol]
    dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for [Anonymous Symbol]
    dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig) refers to dl_adc12.o(.text.DL_ADC12_getClockConfig) for [Anonymous Symbol]
    dl_interrupt.o(.text.DL_Interrupt_registerInterrupt) refers to dl_interrupt.o(.vtable) for [Anonymous Symbol]
    dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_registerInterrupt) refers to dl_interrupt.o(.text.DL_Interrupt_registerInterrupt) for [Anonymous Symbol]
    dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt) refers to dl_interrupt.o(.vtable) for [Anonymous Symbol]
    dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt) refers to startup_mspm0g350x_uvision.o(.text) for Default_Handler
    dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_unregisterInterrupt) refers to dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt) for [Anonymous Symbol]
    bsp_debugtimer.o(.ARM.exidx.text.get_StartCount) refers to bsp_debugtimer.o(.text.get_StartCount) for [Anonymous Symbol]
    bsp_debugtimer.o(.text.get_Freq) refers to ffltui.o(.text) for __aeabi_ui2f
    bsp_debugtimer.o(.text.get_Freq) refers to fdiv.o(.text) for __aeabi_fdiv
    bsp_debugtimer.o(.text.get_Freq) refers to roundf.o(i.roundf) for roundf
    bsp_debugtimer.o(.text.get_Freq) refers to ffixi.o(.text) for __aeabi_f2iz
    bsp_debugtimer.o(.ARM.exidx.text.get_Freq) refers to bsp_debugtimer.o(.text.get_Freq) for [Anonymous Symbol]
    bsp_debugtimer.o(.text.get_UsedTime) refers to fflti.o(.text) for __aeabi_i2f
    bsp_debugtimer.o(.text.get_UsedTime) refers to fdiv.o(.text) for __aeabi_fdiv
    bsp_debugtimer.o(.text.get_UsedTime) refers to fmul.o(.text) for __aeabi_fmul
    bsp_debugtimer.o(.ARM.exidx.text.get_UsedTime) refers to bsp_debugtimer.o(.text.get_UsedTime) for [Anonymous Symbol]
    bsp_debugtimer.o(.data.RTOSTaskDebug) refers to bsp_debugtimer.o(.text.get_StartCount) for get_StartCount
    bsp_debugtimer.o(.data.RTOSTaskDebug) refers to bsp_debugtimer.o(.text.get_Freq) for get_Freq
    bsp_debugtimer.o(.data.RTOSTaskDebug) refers to bsp_debugtimer.o(.text.get_UsedTime) for get_UsedTime
    bsp_iic.o(.ARM.exidx.text.mspm0_get_clock_ms) refers to bsp_iic.o(.text.mspm0_get_clock_ms) for [Anonymous Symbol]
    bsp_iic.o(.text.mpu6050_i2c_sda_unlock) refers to bsp_systick.o(.text.delay_ms) for delay_ms
    bsp_iic.o(.text.mpu6050_i2c_sda_unlock) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) for SYSCFG_DL_I2C_0_init
    bsp_iic.o(.ARM.exidx.text.mpu6050_i2c_sda_unlock) refers to bsp_iic.o(.text.mpu6050_i2c_sda_unlock) for [Anonymous Symbol]
    bsp_iic.o(.text.mspm0_i2c_write) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    bsp_iic.o(.ARM.exidx.text.mspm0_i2c_write) refers to bsp_iic.o(.text.mspm0_i2c_write) for [Anonymous Symbol]
    bsp_iic.o(.text.mspm0_i2c_read) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    bsp_iic.o(.ARM.exidx.text.mspm0_i2c_read) refers to bsp_iic.o(.text.mspm0_i2c_read) for [Anonymous Symbol]
    bsp_iic.o(.text.HW_iic_init) refers to bsp_systick.o(.text.delay_ms) for delay_ms
    bsp_iic.o(.text.HW_iic_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) for SYSCFG_DL_I2C_0_init
    bsp_iic.o(.ARM.exidx.text.HW_iic_init) refers to bsp_iic.o(.text.HW_iic_init) for [Anonymous Symbol]
    bsp_iic.o(.ARM.exidx.text.HW_IIC_Master_Transmit) refers to bsp_iic.o(.text.HW_IIC_Master_Transmit) for [Anonymous Symbol]
    bsp_iic.o(.ARM.exidx.text.HW_IIC_Master_Receive) refers to bsp_iic.o(.text.HW_IIC_Master_Receive) for [Anonymous Symbol]
    bsp_iic.o(.text.HW_IIC_Mem_Write) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    bsp_iic.o(.ARM.exidx.text.HW_IIC_Mem_Write) refers to bsp_iic.o(.text.HW_IIC_Mem_Write) for [Anonymous Symbol]
    bsp_iic.o(.text.HW_IIC_Mem_Read) refers to bsp_iic.o(.text.mspm0_i2c_read) for mspm0_i2c_read
    bsp_iic.o(.ARM.exidx.text.HW_IIC_Mem_Read) refers to bsp_iic.o(.text.HW_IIC_Mem_Read) for [Anonymous Symbol]
    bsp_iic.o(.text.HW_iic_delayms) refers to bsp_systick.o(.text.delay_ms) for delay_ms
    bsp_iic.o(.ARM.exidx.text.HW_iic_delayms) refers to bsp_iic.o(.text.HW_iic_delayms) for [Anonymous Symbol]
    bsp_iic.o(.data.User_sIICDev) refers to bsp_iic.o(.text.HW_iic_init) for HW_iic_init
    bsp_iic.o(.data.User_sIICDev) refers to bsp_iic.o(.text.HW_IIC_Master_Transmit) for HW_IIC_Master_Transmit
    bsp_iic.o(.data.User_sIICDev) refers to bsp_iic.o(.text.HW_IIC_Master_Receive) for HW_IIC_Master_Receive
    bsp_iic.o(.data.User_sIICDev) refers to bsp_iic.o(.text.HW_IIC_Mem_Write) for HW_IIC_Mem_Write
    bsp_iic.o(.data.User_sIICDev) refers to bsp_iic.o(.text.HW_IIC_Mem_Read) for HW_IIC_Mem_Read
    bsp_iic.o(.data.User_sIICDev) refers to bsp_iic.o(.text.HW_iic_delayms) for HW_iic_delayms
    bsp_key.o(.text.key_scan) refers to ffltui.o(.text) for __aeabi_ui2f
    bsp_key.o(.text.key_scan) refers to fdiv.o(.text) for __aeabi_fdiv
    bsp_key.o(.text.key_scan) refers to fmul.o(.text) for __aeabi_fmul
    bsp_key.o(.text.key_scan) refers to ffixi.o(.text) for __aeabi_f2iz
    bsp_key.o(.text.key_scan) refers to bsp_key.o(.bss.key_scan.check_once) for [Anonymous Symbol]
    bsp_key.o(.text.key_scan) refers to bsp_key.o(.bss.key_scan.long_press_time) for [Anonymous Symbol]
    bsp_key.o(.text.key_scan) refers to bsp_key.o(.bss.key_scan.time_core) for [Anonymous Symbol]
    bsp_key.o(.text.key_scan) refers to bsp_key.o(.bss.key_scan.press_flag) for [Anonymous Symbol]
    bsp_key.o(.ARM.exidx.text.key_scan) refers to bsp_key.o(.text.key_scan) for [Anonymous Symbol]
    bsp_key.o(.data.UserKey) refers to bsp_key.o(.text.key_scan) for key_scan
    bsp_oled.o(.text.OLED_I2C_Start) refers to bsp_systick.o(.text.delay_us) for delay_us
    bsp_oled.o(.ARM.exidx.text.OLED_I2C_Start) refers to bsp_oled.o(.text.OLED_I2C_Start) for [Anonymous Symbol]
    bsp_oled.o(.text.OLED_I2C_Stop) refers to bsp_systick.o(.text.delay_us) for delay_us
    bsp_oled.o(.ARM.exidx.text.OLED_I2C_Stop) refers to bsp_oled.o(.text.OLED_I2C_Stop) for [Anonymous Symbol]
    bsp_oled.o(.text.OLED_I2C_SendByte) refers to bsp_systick.o(.text.delay_us) for delay_us
    bsp_oled.o(.ARM.exidx.text.OLED_I2C_SendByte) refers to bsp_oled.o(.text.OLED_I2C_SendByte) for [Anonymous Symbol]
    bsp_oled.o(.text.OLED_WriteCommand) refers to bsp_systick.o(.text.delay_us) for delay_us
    bsp_oled.o(.text.OLED_WriteCommand) refers to bsp_oled.o(.text.OLED_I2C_SendByte) for OLED_I2C_SendByte
    bsp_oled.o(.ARM.exidx.text.OLED_WriteCommand) refers to bsp_oled.o(.text.OLED_WriteCommand) for [Anonymous Symbol]
    bsp_oled.o(.text.OLED_WriteData) refers to bsp_systick.o(.text.delay_us) for delay_us
    bsp_oled.o(.text.OLED_WriteData) refers to bsp_oled.o(.text.OLED_I2C_SendByte) for OLED_I2C_SendByte
    bsp_oled.o(.ARM.exidx.text.OLED_WriteData) refers to bsp_oled.o(.text.OLED_WriteData) for [Anonymous Symbol]
    bsp_oled.o(.text.OLED_SetCursor) refers to bsp_systick.o(.text.delay_us) for delay_us
    bsp_oled.o(.text.OLED_SetCursor) refers to bsp_oled.o(.text.OLED_I2C_SendByte) for OLED_I2C_SendByte
    bsp_oled.o(.ARM.exidx.text.OLED_SetCursor) refers to bsp_oled.o(.text.OLED_SetCursor) for [Anonymous Symbol]
    bsp_oled.o(.text.OLED_Clear) refers to bsp_oled.o(.text.OLED_SetCursor) for OLED_SetCursor
    bsp_oled.o(.text.OLED_Clear) refers to bsp_systick.o(.text.delay_us) for delay_us
    bsp_oled.o(.text.OLED_Clear) refers to bsp_oled.o(.text.OLED_I2C_SendByte) for OLED_I2C_SendByte
    bsp_oled.o(.ARM.exidx.text.OLED_Clear) refers to bsp_oled.o(.text.OLED_Clear) for [Anonymous Symbol]
    bsp_oled.o(.text.OLED_ShowChar) refers to bsp_oled.o(.text.OLED_SetCursor) for OLED_SetCursor
    bsp_oled.o(.text.OLED_ShowChar) refers to bsp_systick.o(.text.delay_us) for delay_us
    bsp_oled.o(.text.OLED_ShowChar) refers to bsp_oled.o(.text.OLED_I2C_SendByte) for OLED_I2C_SendByte
    bsp_oled.o(.text.OLED_ShowChar) refers to bsp_oled.o(.rodata.OLED_F8x16) for OLED_F8x16
    bsp_oled.o(.ARM.exidx.text.OLED_ShowChar) refers to bsp_oled.o(.text.OLED_ShowChar) for [Anonymous Symbol]
    bsp_oled.o(.text.OLED_ShowString) refers to bsp_oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    bsp_oled.o(.ARM.exidx.text.OLED_ShowString) refers to bsp_oled.o(.text.OLED_ShowString) for [Anonymous Symbol]
    bsp_oled.o(.ARM.exidx.text.OLED_Pow) refers to bsp_oled.o(.text.OLED_Pow) for [Anonymous Symbol]
    bsp_oled.o(.text.OLED_ShowNum) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    bsp_oled.o(.text.OLED_ShowNum) refers to bsp_oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    bsp_oled.o(.ARM.exidx.text.OLED_ShowNum) refers to bsp_oled.o(.text.OLED_ShowNum) for [Anonymous Symbol]
    bsp_oled.o(.text.OLED_ShowSignedNum) refers to bsp_oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    bsp_oled.o(.text.OLED_ShowSignedNum) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    bsp_oled.o(.ARM.exidx.text.OLED_ShowSignedNum) refers to bsp_oled.o(.text.OLED_ShowSignedNum) for [Anonymous Symbol]
    bsp_oled.o(.text.OLED_Init) refers to bsp_systick.o(.text.delay_us) for delay_us
    bsp_oled.o(.text.OLED_Init) refers to bsp_oled.o(.text.OLED_I2C_SendByte) for OLED_I2C_SendByte
    bsp_oled.o(.text.OLED_Init) refers to bsp_systick.o(.text.delay_ms) for delay_ms
    bsp_oled.o(.text.OLED_Init) refers to bsp_oled.o(.text.OLED_Clear) for OLED_Clear
    bsp_oled.o(.ARM.exidx.text.OLED_Init) refers to bsp_oled.o(.text.OLED_Init) for [Anonymous Symbol]
    bsp_oled.o(.text.OLED_ShowChar_Wrapper) refers to bsp_oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    bsp_oled.o(.ARM.exidx.text.OLED_ShowChar_Wrapper) refers to bsp_oled.o(.text.OLED_ShowChar_Wrapper) for [Anonymous Symbol]
    bsp_oled.o(.text.OLED_ShowNumber_Wrapper) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    bsp_oled.o(.text.OLED_ShowNumber_Wrapper) refers to bsp_oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    bsp_oled.o(.ARM.exidx.text.OLED_ShowNumber_Wrapper) refers to bsp_oled.o(.text.OLED_ShowNumber_Wrapper) for [Anonymous Symbol]
    bsp_oled.o(.text.OLED_ShowString_Wrapper) refers to bsp_oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    bsp_oled.o(.ARM.exidx.text.OLED_ShowString_Wrapper) refers to bsp_oled.o(.text.OLED_ShowString_Wrapper) for [Anonymous Symbol]
    bsp_oled.o(.text.OLED_ShowFloat_Wrapper) refers to fcmplt.o(.text) for __aeabi_fcmplt
    bsp_oled.o(.text.OLED_ShowFloat_Wrapper) refers to bsp_oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    bsp_oled.o(.text.OLED_ShowFloat_Wrapper) refers to ffixi.o(.text) for __aeabi_f2iz
    bsp_oled.o(.text.OLED_ShowFloat_Wrapper) refers to fflti.o(.text) for __aeabi_i2f
    bsp_oled.o(.text.OLED_ShowFloat_Wrapper) refers to fadd.o(.text) for __aeabi_fsub
    bsp_oled.o(.text.OLED_ShowFloat_Wrapper) refers to fmul.o(.text) for __aeabi_fmul
    bsp_oled.o(.text.OLED_ShowFloat_Wrapper) refers to ffixui.o(.text) for __aeabi_f2uiz
    bsp_oled.o(.text.OLED_ShowFloat_Wrapper) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    bsp_oled.o(.ARM.exidx.text.OLED_ShowFloat_Wrapper) refers to bsp_oled.o(.text.OLED_ShowFloat_Wrapper) for [Anonymous Symbol]
    bsp_oled.o(.ARM.exidx.text.OLED_RefreshGram_Wrapper) refers to bsp_oled.o(.text.OLED_RefreshGram_Wrapper) for [Anonymous Symbol]
    bsp_oled.o(.data.UserOLED) refers to bsp_oled.o(.text.OLED_Init) for OLED_Init
    bsp_oled.o(.data.UserOLED) refers to bsp_oled.o(.text.OLED_ShowChar_Wrapper) for OLED_ShowChar_Wrapper
    bsp_oled.o(.data.UserOLED) refers to bsp_oled.o(.text.OLED_ShowNumber_Wrapper) for OLED_ShowNumber_Wrapper
    bsp_oled.o(.data.UserOLED) refers to bsp_oled.o(.text.OLED_ShowString_Wrapper) for OLED_ShowString_Wrapper
    bsp_oled.o(.data.UserOLED) refers to bsp_oled.o(.text.OLED_ShowFloat_Wrapper) for OLED_ShowFloat_Wrapper
    bsp_oled.o(.data.UserOLED) refers to bsp_oled.o(.text.OLED_RefreshGram_Wrapper) for OLED_RefreshGram_Wrapper
    bsp_oled.o(.data.UserOLED) refers to bsp_oled.o(.text.OLED_Clear) for OLED_Clear
    bsp_printf.o(.text.any_printf) refers to printfa.o(i.__0vsprintf) for vsprintf
    bsp_printf.o(.text.any_printf) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    bsp_printf.o(.text.any_printf) refers to bsp_printf.o(.bss.Ux_TxBuff) for Ux_TxBuff
    bsp_printf.o(.ARM.exidx.text.any_printf) refers to bsp_printf.o(.text.any_printf) for [Anonymous Symbol]
    bsp_printf.o(.text.fputc) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    bsp_printf.o(.ARM.exidx.text.fputc) refers to bsp_printf.o(.text.fputc) for [Anonymous Symbol]
    bsp_printf.o(.text.fputs) refers to strlen.o(.text) for strlen
    bsp_printf.o(.text.fputs) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    bsp_printf.o(.ARM.exidx.text.fputs) refers to bsp_printf.o(.text.fputs) for [Anonymous Symbol]
    bsp_printf.o(.text.puts) refers to strlen.o(.text) for strlen
    bsp_printf.o(.text.puts) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    bsp_printf.o(.ARM.exidx.text.puts) refers to bsp_printf.o(.text.puts) for [Anonymous Symbol]
    bsp_systick.o(.ARM.exidx.text.Systick_getTick) refers to bsp_systick.o(.text.Systick_getTick) for [Anonymous Symbol]
    bsp_systick.o(.ARM.exidx.text.delay_ms) refers to bsp_systick.o(.text.delay_ms) for [Anonymous Symbol]
    bsp_systick.o(.ARM.exidx.text.delay_us) refers to bsp_systick.o(.text.delay_us) for [Anonymous Symbol]
    inv_mpu.o(.text.myi2cRead) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.myi2cRead) refers to inv_mpu.o(.text.myi2cRead) for [Anonymous Symbol]
    inv_mpu.o(.text.myi2cWrite) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.myi2cWrite) refers to inv_mpu.o(.text.myi2cWrite) for [Anonymous Symbol]
    inv_mpu.o(.text.mydelay_ms) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.mydelay_ms) refers to inv_mpu.o(.text.mydelay_ms) for [Anonymous Symbol]
    inv_mpu.o(.text.set_int_enable) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.set_int_enable) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.set_int_enable) refers to inv_mpu.o(.text.set_int_enable) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_reg_dump) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_reg_dump) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.mpu_reg_dump) refers to inv_mpu.o(.text.mpu_reg_dump) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_read_reg) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_read_reg) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.mpu_read_reg) refers to inv_mpu.o(.text.mpu_read_reg) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.text.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.text.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.text.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.text.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.text.mpu_set_bypass) for mpu_set_bypass
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.text.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(.text.mpu_init) refers to bsp_printf.o(.text.puts) for puts
    inv_mpu.o(.text.mpu_init) refers to printfa.o(i.__0printf) for __2printf
    inv_mpu.o(.text.mpu_init) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.rodata.str1.1) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_init) refers to inv_mpu.o(.text.mpu_init) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_gyro_fsr) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_gyro_fsr) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.mpu_set_gyro_fsr) refers to inv_mpu.o(.text.mpu_set_gyro_fsr) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_accel_fsr) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_accel_fsr) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_fsr) refers to inv_mpu.o(.text.mpu_set_accel_fsr) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_lpf) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_lpf) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.mpu_set_lpf) refers to inv_mpu.o(.text.mpu_set_lpf) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_sample_rate) refers to inv_mpu.o(.text.mpu_lp_accel_mode) for mpu_lp_accel_mode
    inv_mpu.o(.text.mpu_set_sample_rate) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    inv_mpu.o(.text.mpu_set_sample_rate) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_sample_rate) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.mpu_set_sample_rate) refers to inv_mpu.o(.text.mpu_set_sample_rate) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_configure_fifo) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(.text.mpu_configure_fifo) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_configure_fifo) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.mpu_configure_fifo) refers to inv_mpu.o(.text.mpu_configure_fifo) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_bypass) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_bypass) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.mpu_set_bypass) refers to inv_mpu.o(.text.mpu_set_bypass) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_sensors) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.text.mpu_set_sensors) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_sensors) refers to inv_mpu.o(.text.mpu_set_sensors) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_lp_accel_mode) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_lp_accel_mode) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.mpu_lp_accel_mode) refers to inv_mpu.o(.text.mpu_lp_accel_mode) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_int_latched) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_int_latched) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_latched) refers to inv_mpu.o(.text.mpu_set_int_latched) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_gyro_reg) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_gyro_reg) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_reg) refers to inv_mpu.o(.text.mpu_get_gyro_reg) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.myget_ms) refers to inv_mpu.o(.text.myget_ms) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_accel_reg) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_accel_reg) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_reg) refers to inv_mpu.o(.text.mpu_get_accel_reg) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_temperature) refers to fflti.o(.text) for __aeabi_i2f
    inv_mpu.o(.text.mpu_get_temperature) refers to fadd.o(.text) for __aeabi_fsub
    inv_mpu.o(.text.mpu_get_temperature) refers to ffltui.o(.text) for __aeabi_ui2f
    inv_mpu.o(.text.mpu_get_temperature) refers to fdiv.o(.text) for __aeabi_fdiv
    inv_mpu.o(.text.mpu_get_temperature) refers to fmul.o(.text) for __aeabi_fmul
    inv_mpu.o(.text.mpu_get_temperature) refers to ffixi.o(.text) for __aeabi_f2iz
    inv_mpu.o(.text.mpu_get_temperature) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_temperature) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.mpu_get_temperature) refers to inv_mpu.o(.text.mpu_get_temperature) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_accel_bias) refers to idiv_div0.o(.text) for __aeabi_idiv
    inv_mpu.o(.text.mpu_set_accel_bias) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.text.mpu_set_accel_bias) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_bias) refers to inv_mpu.o(.text.mpu_set_accel_bias) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_reset_fifo) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_reset_fifo) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.mpu_reset_fifo) refers to inv_mpu.o(.text.mpu_reset_fifo) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_gyro_fsr) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_gyro_fsr) refers to inv_mpu.o(.rodata.cst8) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_fsr) refers to inv_mpu.o(.text.mpu_get_gyro_fsr) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_accel_fsr) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_fsr) refers to inv_mpu.o(.text.mpu_get_accel_fsr) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_lpf) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_lpf) refers to inv_mpu.o(.rodata..Lswitch.table.mpu_lp_motion_interrupt.7) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_lpf) refers to inv_mpu.o(.text.mpu_get_lpf) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_sample_rate) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_sample_rate) refers to inv_mpu.o(.text.mpu_get_sample_rate) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_sample_rate) refers to inv_mpu.o(.text.mpu_get_compass_sample_rate) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_compass_sample_rate) refers to inv_mpu.o(.text.mpu_set_compass_sample_rate) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_gyro_sens) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_sens) refers to inv_mpu.o(.text.mpu_get_gyro_sens) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_accel_sens) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_sens) refers to inv_mpu.o(.text.mpu_get_accel_sens) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_fifo_config) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_fifo_config) refers to inv_mpu.o(.text.mpu_get_fifo_config) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_power_state) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_power_state) refers to inv_mpu.o(.text.mpu_get_power_state) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_int_status) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_int_status) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.mpu_get_int_status) refers to inv_mpu.o(.text.mpu_get_int_status) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_read_fifo) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    inv_mpu.o(.text.mpu_read_fifo) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(.text.mpu_read_fifo) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_read_fifo) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo) refers to inv_mpu.o(.text.mpu_read_fifo) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_read_fifo_stream) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    inv_mpu.o(.text.mpu_read_fifo_stream) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(.text.mpu_read_fifo_stream) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_read_fifo_stream) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo_stream) refers to inv_mpu.o(.text.mpu_read_fifo_stream) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_int_level) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_level) refers to inv_mpu.o(.text.mpu_set_int_level) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.get_st_biases) for get_st_biases
    inv_mpu.o(.text.mpu_run_self_test) refers to fmul.o(.text) for __aeabi_fmul
    inv_mpu.o(.text.mpu_run_self_test) refers to fflti.o(.text) for __aeabi_i2f
    inv_mpu.o(.text.mpu_run_self_test) refers to fcmpeq.o(.text) for __aeabi_fcmpeq
    inv_mpu.o(.text.mpu_run_self_test) refers to fdiv.o(.text) for __aeabi_fdiv
    inv_mpu.o(.text.mpu_run_self_test) refers to fadd.o(.text) for __aeabi_fadd
    inv_mpu.o(.text.mpu_run_self_test) refers to fcmple.o(.text) for __aeabi_fcmple
    inv_mpu.o(.text.mpu_run_self_test) refers to fcmpgt.o(.text) for __aeabi_fcmpgt
    inv_mpu.o(.text.mpu_run_self_test) refers to fcmplt.o(.text) for __aeabi_fcmplt
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.rodata.cst8) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_run_self_test) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(.ARM.exidx.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_run_self_test) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_dmp_state) refers to inv_mpu.o(.text.mpu_set_bypass) for mpu_set_bypass
    inv_mpu.o(.text.mpu_set_dmp_state) refers to inv_mpu.o(.text.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(.text.mpu_set_dmp_state) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(.text.mpu_set_dmp_state) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_dmp_state) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.mpu_set_dmp_state) refers to inv_mpu.o(.text.mpu_set_dmp_state) for [Anonymous Symbol]
    inv_mpu.o(.text.get_st_biases) refers to ldiv.o(.text) for __aeabi_ldivmod
    inv_mpu.o(.text.get_st_biases) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.text.get_st_biases) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.get_st_biases) refers to inv_mpu.o(.text.get_st_biases) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_write_mem) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_write_mem) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.mpu_write_mem) refers to inv_mpu.o(.text.mpu_write_mem) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_read_mem) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_read_mem) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.mpu_read_mem) refers to inv_mpu.o(.text.mpu_read_mem) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_load_firmware) refers to memcmp.o(.text) for memcmp
    inv_mpu.o(.text.mpu_load_firmware) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_load_firmware) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.mpu_load_firmware) refers to inv_mpu.o(.text.mpu_load_firmware) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_dmp_state) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_dmp_state) refers to inv_mpu.o(.text.mpu_get_dmp_state) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_reg) refers to inv_mpu.o(.text.mpu_get_compass_reg) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_fsr) refers to inv_mpu.o(.text.mpu_get_compass_fsr) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_lp_accel_mode) for mpu_lp_accel_mode
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.rodata.cst8) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.rodata..Lswitch.table.mpu_lp_motion_interrupt.7) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    inv_mpu.o(.ARM.exidx.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_lp_motion_interrupt) for [Anonymous Symbol]
    inv_mpu.o(.data.st) refers to inv_mpu.o(.rodata.reg) for reg
    inv_mpu.o(.data.st) refers to inv_mpu.o(.rodata.hw) for hw
    inv_mpu.o(.data.st) refers to inv_mpu.o(.rodata.test) for test
    inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware) refers to inv_mpu.o(.text.mpu_load_firmware) for mpu_load_firmware
    inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware) refers to inv_mpu_dmp_motion_driver.o(.rodata.dmp_memory) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_load_motion_driver_firmware) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation) refers to inv_mpu_dmp_motion_driver.o(.rodata..L__const.dmp_set_orientation.gyro_axes) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation) refers to inv_mpu_dmp_motion_driver.o(.rodata..L__const.dmp_set_orientation.accel_axes) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.2) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_orientation) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias) refers to llmul.o(.text) for __aeabi_lmul
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.2) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_gyro_bias) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias) refers to inv_mpu.o(.text.mpu_get_accel_sens) for mpu_get_accel_sens
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias) refers to llmul.o(.text) for __aeabi_lmul
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.2) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_accel_bias) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.4) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_get_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.4) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_get_fifo_rate) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) refers to inv_mpu.o(.text.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) refers to ffltui.o(.text) for __aeabi_ui2f
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) refers to fdiv.o(.text) for __aeabi_fdiv
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) refers to fmul.o(.text) for __aeabi_fmul
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) refers to ffixi.o(.text) for __aeabi_f2iz
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_thresh) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_axes) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_axes) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_axes) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_count) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_count) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_count) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_time) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time_multi) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_time_multi) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time_multi) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_thresh) refers to idiv_div0.o(.text) for __aeabi_idiv
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_thresh) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_shake_reject_thresh) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_thresh) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_time) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_shake_reject_time) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_time) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_timeout) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_shake_reject_timeout) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_timeout) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_get_pedometer_step_count) refers to inv_mpu.o(.text.mpu_read_mem) for mpu_read_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_pedometer_step_count) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_get_pedometer_step_count) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_step_count) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_pedometer_step_count) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_step_count) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_get_pedometer_walk_time) refers to inv_mpu.o(.text.mpu_read_mem) for mpu_read_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_pedometer_walk_time) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_get_pedometer_walk_time) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_walk_time) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_walk_time) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_pedometer_walk_time) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_walk_time) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) for dmp_set_tap_thresh
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.3) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.5) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_gyro_cal) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_gyro_cal) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_enable_gyro_cal) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_lp_quat) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_lp_quat) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_lp_quat) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_enable_lp_quat) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_6x_lp_quat) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_6x_lp_quat) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_6x_lp_quat) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_enable_6x_lp_quat) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_get_enabled_features) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.3) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_enabled_features) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_get_enabled_features) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_interrupt_mode) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_interrupt_mode) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_interrupt_mode) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu.o(.text.mpu_read_fifo_stream) for mpu_read_fifo_stream
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu.o(.text.myget_ms) for myget_ms
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.5) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.3) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.0) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.1) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_register_tap_cb) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.0) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_register_tap_cb) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_register_tap_cb) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_register_android_orient_cb) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp.1) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_register_android_orient_cb) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_register_android_orient_cb) for [Anonymous Symbol]
    kf.o(.text.KF_X) refers to f2d.o(.text) for __aeabi_f2d
    kf.o(.text.KF_X) refers to atan2.o(i.atan2) for atan2
    kf.o(.text.KF_X) refers to fmul.o(.text) for __aeabi_fmul
    kf.o(.text.KF_X) refers to fadd.o(.text) for __aeabi_fadd
    kf.o(.text.KF_X) refers to fdiv.o(.text) for __aeabi_fdiv
    kf.o(.text.KF_X) refers to d2f.o(.text) for __aeabi_d2f
    kf.o(.text.KF_X) refers to kf.o(.bss.KF_X.p_hat.2) for [Anonymous Symbol]
    kf.o(.text.KF_X) refers to kf.o(.data.KF_X.p_hat.0) for [Anonymous Symbol]
    kf.o(.text.KF_X) refers to kf.o(.data.KF_X.p_hat.3) for [Anonymous Symbol]
    kf.o(.text.KF_X) refers to kf.o(.bss.KF_X.p_hat.1) for [Anonymous Symbol]
    kf.o(.text.KF_X) refers to kf.o(.bss.KF_X.x_hat.1) for [Anonymous Symbol]
    kf.o(.text.KF_X) refers to kf.o(.bss.KF_X.x_hat.0) for [Anonymous Symbol]
    kf.o(.ARM.exidx.text.KF_X) refers to kf.o(.text.KF_X) for [Anonymous Symbol]
    kf.o(.text.mul) refers to fmul.o(.text) for __aeabi_fmul
    kf.o(.text.mul) refers to fadd.o(.text) for __aeabi_fadd
    kf.o(.ARM.exidx.text.mul) refers to kf.o(.text.mul) for [Anonymous Symbol]
    kf.o(.text.KF_Y) refers to f2d.o(.text) for __aeabi_f2d
    kf.o(.text.KF_Y) refers to atan2.o(i.atan2) for atan2
    kf.o(.text.KF_Y) refers to fmul.o(.text) for __aeabi_fmul
    kf.o(.text.KF_Y) refers to fadd.o(.text) for __aeabi_fadd
    kf.o(.text.KF_Y) refers to fdiv.o(.text) for __aeabi_fdiv
    kf.o(.text.KF_Y) refers to d2f.o(.text) for __aeabi_d2f
    kf.o(.text.KF_Y) refers to kf.o(.bss.KF_Y.p_hat.2) for [Anonymous Symbol]
    kf.o(.text.KF_Y) refers to kf.o(.data.KF_Y.p_hat.0) for [Anonymous Symbol]
    kf.o(.text.KF_Y) refers to kf.o(.data.KF_Y.p_hat.3) for [Anonymous Symbol]
    kf.o(.text.KF_Y) refers to kf.o(.bss.KF_Y.p_hat.1) for [Anonymous Symbol]
    kf.o(.text.KF_Y) refers to kf.o(.bss.KF_Y.x_hat.1) for [Anonymous Symbol]
    kf.o(.text.KF_Y) refers to kf.o(.bss.KF_Y.x_hat.0) for [Anonymous Symbol]
    kf.o(.ARM.exidx.text.KF_Y) refers to kf.o(.text.KF_Y) for [Anonymous Symbol]
    mpu6050.o(.text.IICwriteBits) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    mpu6050.o(.ARM.exidx.text.IICwriteBits) refers to mpu6050.o(.text.IICwriteBits) for [Anonymous Symbol]
    mpu6050.o(.text.IICwriteBit) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    mpu6050.o(.ARM.exidx.text.IICwriteBit) refers to mpu6050.o(.text.IICwriteBit) for [Anonymous Symbol]
    mpu6050.o(.text.IICreadBytes) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    mpu6050.o(.ARM.exidx.text.IICreadBytes) refers to mpu6050.o(.text.IICreadBytes) for [Anonymous Symbol]
    mpu6050.o(.text.i2cRead) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    mpu6050.o(.ARM.exidx.text.i2cRead) refers to mpu6050.o(.text.i2cRead) for [Anonymous Symbol]
    mpu6050.o(.text.I2C_ReadOneByte) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    mpu6050.o(.ARM.exidx.text.I2C_ReadOneByte) refers to mpu6050.o(.text.I2C_ReadOneByte) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_newValues) refers to memmovea.o(.text) for __aeabi_memmove
    mpu6050.o(.text.MPU6050_newValues) refers to idiv_div0.o(.text) for __aeabi_idiv
    mpu6050.o(.text.MPU6050_newValues) refers to mpu6050.o(.bss.MPU6050_FIFO) for MPU6050_FIFO
    mpu6050.o(.ARM.exidx.text.MPU6050_newValues) refers to mpu6050.o(.text.MPU6050_newValues) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_setClockSource) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    mpu6050.o(.ARM.exidx.text.MPU6050_setClockSource) refers to mpu6050.o(.text.MPU6050_setClockSource) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_setFullScaleGyroRange) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    mpu6050.o(.ARM.exidx.text.MPU6050_setFullScaleGyroRange) refers to mpu6050.o(.text.MPU6050_setFullScaleGyroRange) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_setFullScaleAccelRange) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    mpu6050.o(.ARM.exidx.text.MPU6050_setFullScaleAccelRange) refers to mpu6050.o(.text.MPU6050_setFullScaleAccelRange) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_setSleepEnabled) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    mpu6050.o(.ARM.exidx.text.MPU6050_setSleepEnabled) refers to mpu6050.o(.text.MPU6050_setSleepEnabled) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_getDeviceID) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    mpu6050.o(.text.MPU6050_getDeviceID) refers to mpu6050.o(.bss.buffer) for buffer
    mpu6050.o(.ARM.exidx.text.MPU6050_getDeviceID) refers to mpu6050.o(.text.MPU6050_getDeviceID) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_testConnection) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    mpu6050.o(.text.MPU6050_testConnection) refers to mpu6050.o(.bss.buffer) for buffer
    mpu6050.o(.ARM.exidx.text.MPU6050_testConnection) refers to mpu6050.o(.text.MPU6050_testConnection) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_setI2CMasterModeEnabled) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    mpu6050.o(.ARM.exidx.text.MPU6050_setI2CMasterModeEnabled) refers to mpu6050.o(.text.MPU6050_setI2CMasterModeEnabled) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_setI2CBypassEnabled) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    mpu6050.o(.ARM.exidx.text.MPU6050_setI2CBypassEnabled) refers to mpu6050.o(.text.MPU6050_setI2CBypassEnabled) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_initialize) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    mpu6050.o(.text.MPU6050_initialize) refers to mpu6050.o(.bss.buffer) for buffer
    mpu6050.o(.ARM.exidx.text.MPU6050_initialize) refers to mpu6050.o(.text.MPU6050_initialize) for [Anonymous Symbol]
    mpu6050.o(.text.DMP_Init) refers to bsp_printf.o(.text.puts) for puts
    mpu6050.o(.text.DMP_Init) refers to inv_mpu.o(.text.mpu_init) for mpu_init
    mpu6050.o(.text.DMP_Init) refers to inv_mpu.o(.text.mpu_set_sensors) for mpu_set_sensors
    mpu6050.o(.text.DMP_Init) refers to inv_mpu.o(.text.mpu_configure_fifo) for mpu_configure_fifo
    mpu6050.o(.text.DMP_Init) refers to inv_mpu.o(.text.mpu_set_sample_rate) for mpu_set_sample_rate
    mpu6050.o(.text.DMP_Init) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware) for dmp_load_motion_driver_firmware
    mpu6050.o(.text.DMP_Init) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation) for dmp_set_orientation
    mpu6050.o(.text.DMP_Init) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) for dmp_enable_feature
    mpu6050.o(.text.DMP_Init) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate) for dmp_set_fifo_rate
    mpu6050.o(.text.DMP_Init) refers to inv_mpu.o(.text.mpu_run_self_test) for mpu_run_self_test
    mpu6050.o(.text.DMP_Init) refers to inv_mpu.o(.text.mpu_get_gyro_sens) for mpu_get_gyro_sens
    mpu6050.o(.text.DMP_Init) refers to fflti.o(.text) for __aeabi_i2f
    mpu6050.o(.text.DMP_Init) refers to fmul.o(.text) for __aeabi_fmul
    mpu6050.o(.text.DMP_Init) refers to ffixi.o(.text) for __aeabi_f2iz
    mpu6050.o(.text.DMP_Init) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias) for dmp_set_gyro_bias
    mpu6050.o(.text.DMP_Init) refers to inv_mpu.o(.text.mpu_get_accel_sens) for mpu_get_accel_sens
    mpu6050.o(.text.DMP_Init) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias) for dmp_set_accel_bias
    mpu6050.o(.text.DMP_Init) refers to inv_mpu.o(.text.mpu_set_dmp_state) for mpu_set_dmp_state
    mpu6050.o(.text.DMP_Init) refers to bsp_iic.o(.text.mpu6050_i2c_sda_unlock) for mpu6050_i2c_sda_unlock
    mpu6050.o(.text.DMP_Init) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    mpu6050.o(.text.DMP_Init) refers to mpu6050.o(.rodata.str1.1) for [Anonymous Symbol]
    mpu6050.o(.ARM.exidx.text.DMP_Init) refers to mpu6050.o(.text.DMP_Init) for [Anonymous Symbol]
    mpu6050.o(.text.Read_DMP) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) for dmp_read_fifo
    mpu6050.o(.text.Read_DMP) refers to fflti.o(.text) for __aeabi_i2f
    mpu6050.o(.text.Read_DMP) refers to fmul.o(.text) for __aeabi_fmul
    mpu6050.o(.text.Read_DMP) refers to fadd.o(.text) for __aeabi_fadd
    mpu6050.o(.text.Read_DMP) refers to f2d.o(.text) for __aeabi_f2d
    mpu6050.o(.text.Read_DMP) refers to asin.o(i.asin) for asin
    mpu6050.o(.text.Read_DMP) refers to dmul.o(.text) for __aeabi_dmul
    mpu6050.o(.text.Read_DMP) refers to d2f.o(.text) for __aeabi_d2f
    mpu6050.o(.text.Read_DMP) refers to atan2.o(i.atan2) for atan2
    mpu6050.o(.text.Read_DMP) refers to mpu6050.o(.bss.sensors) for sensors
    mpu6050.o(.text.Read_DMP) refers to mpu6050.o(.bss.gyro) for gyro
    mpu6050.o(.text.Read_DMP) refers to mpu6050.o(.bss.accel) for accel
    mpu6050.o(.text.Read_DMP) refers to mpu6050.o(.data.q0) for q0
    mpu6050.o(.text.Read_DMP) refers to mpu6050.o(.bss.q1) for q1
    mpu6050.o(.text.Read_DMP) refers to mpu6050.o(.bss.q2) for q2
    mpu6050.o(.text.Read_DMP) refers to mpu6050.o(.bss.q3) for q3
    mpu6050.o(.text.Read_DMP) refers to mpu6050.o(.bss.Roll) for Roll
    mpu6050.o(.text.Read_DMP) refers to mpu6050.o(.bss.Pitch) for Pitch
    mpu6050.o(.text.Read_DMP) refers to mpu6050.o(.bss.Yaw) for Yaw
    mpu6050.o(.ARM.exidx.text.Read_DMP) refers to mpu6050.o(.text.Read_DMP) for [Anonymous Symbol]
    mpu6050.o(.text.Read_Temperature) refers to fflti.o(.text) for __aeabi_i2f
    mpu6050.o(.text.Read_Temperature) refers to fadd.o(.text) for __aeabi_fadd
    mpu6050.o(.text.Read_Temperature) refers to fdiv.o(.text) for __aeabi_fdiv
    mpu6050.o(.text.Read_Temperature) refers to f2d.o(.text) for __aeabi_f2d
    mpu6050.o(.text.Read_Temperature) refers to dadd.o(.text) for __aeabi_dadd
    mpu6050.o(.text.Read_Temperature) refers to dmul.o(.text) for __aeabi_dmul
    mpu6050.o(.text.Read_Temperature) refers to d2f.o(.text) for __aeabi_d2f
    mpu6050.o(.text.Read_Temperature) refers to ffixi.o(.text) for __aeabi_f2iz
    mpu6050.o(.text.Read_Temperature) refers to bsp_iic.o(.data.User_sIICDev) for User_sIICDev
    mpu6050.o(.ARM.exidx.text.Read_Temperature) refers to mpu6050.o(.text.Read_Temperature) for [Anonymous Symbol]
    balance.o(.text.BalanceControlTask) refers to ffltui.o(.text) for __aeabi_ui2f
    balance.o(.text.BalanceControlTask) refers to fmul.o(.text) for __aeabi_fmul
    balance.o(.text.BalanceControlTask) refers to fflti.o(.text) for __aeabi_i2f
    balance.o(.text.BalanceControlTask) refers to fadd.o(.text) for __aeabi_fsub
    balance.o(.text.BalanceControlTask) refers to ffixi.o(.text) for __aeabi_f2iz
    balance.o(.text.BalanceControlTask) refers to dflti.o(.text) for __aeabi_i2d
    balance.o(.text.BalanceControlTask) refers to dmul.o(.text) for __aeabi_dmul
    balance.o(.text.BalanceControlTask) refers to ddiv.o(.text) for __aeabi_ddiv
    balance.o(.text.BalanceControlTask) refers to d2f.o(.text) for __aeabi_d2f
    balance.o(.text.BalanceControlTask) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    balance.o(.text.BalanceControlTask) refers to bsp_key.o(.data.UserKey) for UserKey
    balance.o(.text.BalanceControlTask) refers to balance.o(.data.Flag_Stop) for Flag_Stop
    balance.o(.text.BalanceControlTask) refers to balance.o(.bss.Get_Vol.startflag) for [Anonymous Symbol]
    balance.o(.text.BalanceControlTask) refers to balance.o(.bss.getVolFlag) for getVolFlag
    balance.o(.text.BalanceControlTask) refers to balance.o(.bss.Get_Vol.adcVal) for [Anonymous Symbol]
    balance.o(.text.BalanceControlTask) refers to balance.o(.bss.flag) for flag
    balance.o(.text.BalanceControlTask) refers to balance.o(.bss.controlFlag) for controlFlag
    balance.o(.text.BalanceControlTask) refers to balance.o(.bss.robotVol) for robotVol
    balance.o(.text.BalanceControlTask) refers to empty.o(.bss.g_EncoderACount) for g_EncoderACount
    balance.o(.text.BalanceControlTask) refers to empty.o(.bss.g_EncoderBCount) for g_EncoderBCount
    balance.o(.text.BalanceControlTask) refers to balance.o(.bss.Encoder_Left) for Encoder_Left
    balance.o(.text.BalanceControlTask) refers to balance.o(.bss.Encoder_Right) for Encoder_Right
    balance.o(.text.BalanceControlTask) refers to balance.o(.bss.Velocity1) for Velocity1
    balance.o(.text.BalanceControlTask) refers to balance.o(.data.Velocity_Kp) for Velocity_Kp
    balance.o(.text.BalanceControlTask) refers to balance.o(.bss.BalanceControlTask.Velocity_Pwm_R) for [Anonymous Symbol]
    balance.o(.text.BalanceControlTask) refers to balance.o(.bss.BalanceControlTask.Velocity_Pwm_L) for [Anonymous Symbol]
    balance.o(.text.BalanceControlTask) refers to balance.o(.bss.Velocity_Left) for Velocity_Left
    balance.o(.text.BalanceControlTask) refers to balance.o(.bss.Velocity_Right) for Velocity_Right
    balance.o(.text.BalanceControlTask) refers to balance.o(.bss.Turn1) for Turn1
    balance.o(.text.BalanceControlTask) refers to balance.o(.data.Turn_Kp) for Turn_Kp
    balance.o(.text.BalanceControlTask) refers to balance.o(.bss.Motor_Left) for Motor_Left
    balance.o(.text.BalanceControlTask) refers to balance.o(.bss.Motor_Right) for Motor_Right
    balance.o(.ARM.exidx.text.BalanceControlTask) refers to balance.o(.text.BalanceControlTask) for [Anonymous Symbol]
    balance.o(.text.Velocity2) refers to fadd.o(.text) for __aeabi_fsub
    balance.o(.text.Velocity2) refers to fmul.o(.text) for __aeabi_fmul
    balance.o(.text.Velocity2) refers to balance.o(.data.Velocity_Kp) for Velocity_Kp
    balance.o(.ARM.exidx.text.Velocity2) refers to balance.o(.text.Velocity2) for [Anonymous Symbol]
    balance.o(.text.ADC1_IRQHandler) refers to balance.o(.bss.getVolFlag) for getVolFlag
    balance.o(.ARM.exidx.text.ADC1_IRQHandler) refers to balance.o(.text.ADC1_IRQHandler) for [Anonymous Symbol]
    bluetooth.o(.text.bt_control) refers to dflti.o(.text) for __aeabi_i2d
    bluetooth.o(.text.bt_control) refers to pow.o(i.pow) for pow
    bluetooth.o(.text.bt_control) refers to f2d.o(.text) for __aeabi_f2d
    bluetooth.o(.text.bt_control) refers to dmul.o(.text) for __aeabi_dmul
    bluetooth.o(.text.bt_control) refers to dadd.o(.text) for __aeabi_dadd
    bluetooth.o(.text.bt_control) refers to d2f.o(.text) for __aeabi_d2f
    bluetooth.o(.text.bt_control) refers to memseta.o(.text) for __aeabi_memclr4
    bluetooth.o(.text.bt_control) refers to balance.o(.bss.Flag_back) for Flag_back
    bluetooth.o(.text.bt_control) refers to balance.o(.bss.Flag_front) for Flag_front
    bluetooth.o(.text.bt_control) refers to balance.o(.bss.Flag_Left) for Flag_Left
    bluetooth.o(.text.bt_control) refers to balance.o(.bss.Flag_Right) for Flag_Right
    bluetooth.o(.text.bt_control) refers to balance.o(.data.Flag_Stop) for Flag_Stop
    bluetooth.o(.text.bt_control) refers to bluetooth.o(.bss.bt_control.Receive) for [Anonymous Symbol]
    bluetooth.o(.text.bt_control) refers to bluetooth.o(.bss.bt_control.i) for [Anonymous Symbol]
    bluetooth.o(.text.bt_control) refers to bluetooth.o(.bss.bt_control.j) for [Anonymous Symbol]
    bluetooth.o(.text.bt_control) refers to bluetooth.o(.bss.bt_control.Data) for [Anonymous Symbol]
    bluetooth.o(.text.bt_control) refers to balance.o(.bss.Turn_Kd) for Turn_Kd
    bluetooth.o(.text.bt_control) refers to balance.o(.data.Turn_Kp) for Turn_Kp
    bluetooth.o(.text.bt_control) refers to balance.o(.bss.Velocity_Ki) for Velocity_Ki
    bluetooth.o(.text.bt_control) refers to balance.o(.data.Velocity_Kp) for Velocity_Kp
    bluetooth.o(.text.bt_control) refers to balance.o(.bss.Balance_Kd) for Balance_Kd
    bluetooth.o(.text.bt_control) refers to balance.o(.bss.Balance_Kp) for Balance_Kp
    bluetooth.o(.text.bt_control) refers to bluetooth.o(.bss.PID_Send) for PID_Send
    bluetooth.o(.text.bt_control) refers to bluetooth.o(.bss.bt_control.Flag_PID) for [Anonymous Symbol]
    bluetooth.o(.ARM.exidx.text.bt_control) refers to bluetooth.o(.text.bt_control) for [Anonymous Symbol]
    show.o(.text.oled_show_test) refers to bsp_oled.o(.data.UserOLED) for UserOLED
    show.o(.ARM.exidx.text.oled_show_test) refers to show.o(.text.oled_show_test) for [Anonymous Symbol]
    show.o(.text.oled_show_simple) refers to idiv_div0.o(.text) for __aeabi_idivmod
    show.o(.text.oled_show_simple) refers to show.o(.bss.oled_show_simple.init_display) for [Anonymous Symbol]
    show.o(.text.oled_show_simple) refers to bsp_oled.o(.data.UserOLED) for UserOLED
    show.o(.text.oled_show_simple) refers to show.o(.rodata.str1.1) for [Anonymous Symbol]
    show.o(.text.oled_show_simple) refers to show.o(.bss.oled_show_simple.last_time) for [Anonymous Symbol]
    show.o(.text.oled_show_simple) refers to empty.o(.bss.time) for time
    show.o(.text.oled_show_simple) refers to balance.o(.data.Flag_Stop) for Flag_Stop
    show.o(.text.oled_show_simple) refers to show.o(.data.oled_show_simple.last_flag_stop) for [Anonymous Symbol]
    show.o(.text.oled_show_simple) refers to empty.o(.bss.HD0) for HD0
    show.o(.text.oled_show_simple) refers to empty.o(.bss.HD1) for HD1
    show.o(.text.oled_show_simple) refers to empty.o(.bss.HD2) for HD2
    show.o(.ARM.exidx.text.oled_show_simple) refers to show.o(.text.oled_show_simple) for [Anonymous Symbol]
    show.o(.text.oled_show) refers to fadd.o(.text) for __aeabi_fsub
    show.o(.text.oled_show) refers to idiv_div0.o(.text) for __aeabi_idivmod
    show.o(.text.oled_show) refers to fcmplt.o(.text) for __aeabi_fcmplt
    show.o(.text.oled_show) refers to fcmpgt.o(.text) for __aeabi_fcmpgt
    show.o(.text.oled_show) refers to fcmple.o(.text) for __aeabi_fcmple
    show.o(.text.oled_show) refers to fcmpge.o(.text) for __aeabi_fcmpge
    show.o(.text.oled_show) refers to show.o(.bss.oled_show.display_counter) for [Anonymous Symbol]
    show.o(.text.oled_show) refers to bsp_oled.o(.data.UserOLED) for UserOLED
    show.o(.text.oled_show) refers to show.o(.rodata.str1.1) for [Anonymous Symbol]
    show.o(.text.oled_show) refers to empty.o(.bss.HD2) for HD2
    show.o(.text.oled_show) refers to empty.o(.bss.HD1) for HD1
    show.o(.text.oled_show) refers to empty.o(.bss.HD0) for HD0
    show.o(.text.oled_show) refers to mpu6050.o(.bss.mpu6050) for mpu6050
    show.o(.text.oled_show) refers to empty.o(.bss.m6050init) for m6050init
    show.o(.text.oled_show) refers to empty.o(.bss.fx) for fx
    show.o(.text.oled_show) refers to empty.o(.data.KEY0) for KEY0
    show.o(.text.oled_show) refers to empty.o(.bss.time) for time
    show.o(.text.oled_show) refers to show.o(.data.lfx) for lfx
    show.o(.text.oled_show) refers to balance.o(.bss.Motor_Left) for Motor_Left
    show.o(.text.oled_show) refers to empty.o(.data.xuanti) for xuanti
    show.o(.text.oled_show) refers to balance.o(.bss.Encoder_Left) for Encoder_Left
    show.o(.text.oled_show) refers to balance.o(.bss.Velocity_Right) for Velocity_Right
    show.o(.text.oled_show) refers to empty.o(.bss.gy) for gy
    show.o(.text.oled_show) refers to balance.o(.bss.robotVol) for robotVol
    show.o(.text.oled_show) refers to balance.o(.data.Flag_Stop) for Flag_Stop
    show.o(.ARM.exidx.text.oled_show) refers to show.o(.text.oled_show) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY) for [Anonymous Symbol]
    asin.o(i.__softfp_asin) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin.o(i.__softfp_asin) refers to asin.o(i.asin) for asin
    asin.o(i.asin) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin.o(i.asin) refers to dmul.o(.text) for __aeabi_dmul
    asin.o(i.asin) refers to errno.o(i.__set_errno) for __set_errno
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    asin.o(i.asin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    asin.o(i.asin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin.o(i.asin) refers to dadd.o(.text) for __aeabi_dadd
    asin.o(i.asin) refers to ddiv.o(.text) for __aeabi_ddiv
    asin.o(i.asin) refers to dscalb.o(.text) for __ARM_scalbn
    asin.o(i.asin) refers to sqrt.o(i.sqrt) for sqrt
    asin.o(i.asin) refers to asin.o(.constdata) for .constdata
    asin.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.atan2) for atan2
    atan2.o(i.atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.atan2) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2.o(i.atan2) refers to dadd.o(.text) for __aeabi_dsub
    atan2.o(i.atan2) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2.o(i.atan2) refers to errno.o(i.__set_errno) for __set_errno
    atan2.o(i.atan2) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.__softfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.pow) for pow
    pow.o(i.pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.pow) refers to errno.o(i.__set_errno) for __set_errno
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.pow) refers to ddiv.o(.text) for __aeabi_ddiv
    pow.o(i.pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.pow) refers to dflti.o(.text) for __aeabi_i2d
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.pow) refers to dmul.o(.text) for __aeabi_dmul
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.pow) refers to dadd.o(.text) for __aeabi_dsub
    pow.o(i.pow) refers to dscalb.o(.text) for __ARM_scalbn
    pow.o(i.pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.pow) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow.o(i.pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    roundf.o(i.roundf) refers (Special) to iusefp.o(.text) for __I$use$fp
    roundf.o(i.roundf) refers to frnd.o(.text) for _frnd
    roundf.o(i.roundf) refers to fadd.o(.text) for __aeabi_fsub
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    idiv.o(.text) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    ldiv.o(.text) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to bsp_printf.o(.text.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to bsp_printf.o(.text.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to bsp_printf.o(.text.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to bsp_printf.o(.text.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to bsp_printf.o(.text.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to bsp_printf.o(.text.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to bsp_printf.o(.text.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to bsp_printf.o(.text.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to bsp_printf.o(.text.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to bsp_printf.o(.text.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to bsp_printf.o(.text.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to bsp_printf.o(.text.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to bsp_printf.o(.text.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to bsp_printf.o(.text.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to bsp_printf.o(.text.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to bsp_printf.o(.text.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to bsp_printf.o(.text.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to bsp_printf.o(.text.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to bsp_printf.o(.text.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to bsp_printf.o(.text.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to bsp_printf.o(.text.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to bsp_printf.o(.text.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to bsp_printf.o(.text.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to bsp_printf.o(.text.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to bsp_printf.o(.text.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to bsp_printf.o(.text.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to bsp_printf.o(.text.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to bsp_printf.o(.text.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to bsp_printf.o(.text.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to bsp_printf.o(.text.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to bsp_printf.o(.text.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to bsp_printf.o(.text.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf6.o(i._printf_core) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to bsp_printf.o(.text.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to bsp_printf.o(.text.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to bsp_printf.o(.text.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to bsp_printf.o(.text.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to bsp_printf.o(.text.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to bsp_printf.o(.text.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to bsp_printf.o(.text.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to bsp_printf.o(.text.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to bsp_printf.o(.text.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to bsp_printf.o(.text.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to bsp_printf.o(.text.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to bsp_printf.o(.text.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    fcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fcmplt.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fcmpge.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fcmpgt.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fcmpeq.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    atan.o(i.__softfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to dadd.o(.text) for __aeabi_dadd
    atan.o(i.atan) refers to dscalb.o(.text) for __ARM_scalbn
    atan.o(i.atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dscalb.o(.text) for __ARM_scalbn
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to empty.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to empty.o(.text.main) for main
    idiv_div0.o(.text) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    fepilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    frnd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    frnd.o(.text) refers to fepilogue.o(.text) for _float_round
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to depilogue.o(i.__ARM_clz) for __ARM_clz
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dscalb.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    dsqrt.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round
    depilogue.o(i.__ARM_clz) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing empty.o(.text), (0 bytes).
    Removing empty.o(.ARM.exidx.text.main), (8 bytes).
    Removing empty.o(.ARM.exidx.text.xunji), (8 bytes).
    Removing empty.o(.ARM.exidx.text.GROUP1_IRQHandler), (8 bytes).
    Removing empty.o(.ARM.exidx.text.UART0_IRQHandler), (8 bytes).
    Removing empty.o(.ARM.exidx.text.K210_Receive_Data), (8 bytes).
    Removing empty.o(.ARM.exidx.text.UART2_IRQHandler), (8 bytes).
    Removing empty.o(.bss.i), (4 bytes).
    Removing empty.o(.bss.HD7), (4 bytes).
    Removing empty.o(.bss.ii), (4 bytes).
    Removing empty.o(.ARM.use_no_argv), (4 bytes).
    Removing startup_mspm0g350x_uvision.o(HEAP), (4096 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DebugTimer_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_I2C_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_2_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ADC12_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration), (40 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration), (48 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration), (8 bytes).
    Removing dl_vref.o(.text), (0 bytes).
    Removing dl_vref.o(.text.DL_VREF_configReference), (32 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_configReference), (8 bytes).
    Removing dl_vref.o(.text.DL_VREF_setClockConfig), (18 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_setClockConfig), (8 bytes).
    Removing dl_vref.o(.text.DL_VREF_getClockConfig), (16 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (16 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (68 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (48 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataBlocking), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_drainRXFIFO), (116 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (116 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (116 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (136 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (144 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (168 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).
    Removing dl_trng.o(.text), (0 bytes).
    Removing dl_trng.o(.text.DL_TRNG_saveConfiguration), (44 bytes).
    Removing dl_trng.o(.ARM.exidx.text.DL_TRNG_saveConfiguration), (8 bytes).
    Removing dl_trng.o(.text.DL_TRNG_restoreConfiguration), (72 bytes).
    Removing dl_trng.o(.ARM.exidx.text.DL_TRNG_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text), (0 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getClockConfig), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareCtl), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureMode), (264 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInput), (26 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureTriggerMode), (116 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureCombinedMode), (224 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareMode), (160 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareTriggerMode), (108 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareAction), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareValue), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareCtl), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareAction), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_overrideCCPOut), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInput), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter), (18 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_saveConfiguration), (264 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_restoreConfiguration), (268 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_initPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setFaultSourceConfig), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getFaultSourceConfig), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_saveConfiguration), (344 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_restoreConfiguration), (360 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_configQEIHallInputMode), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode), (8 bytes).
    Removing dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode), (12 bytes).
    Removing dl_spi.o(.text), (0 bytes).
    Removing dl_spi.o(.text.DL_SPI_init), (68 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_init), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_setClockConfig), (18 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_getClockConfig), (16 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking8), (48 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking16), (48 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking32), (44 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking8), (44 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking16), (44 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking32), (44 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck8), (32 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck16), (32 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck32), (32 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck8), (32 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck16), (32 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck32), (32 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO8), (128 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO16), (128 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO32), (128 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO8), (128 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO16), (124 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_saveConfiguration), (104 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_restoreConfiguration), (128 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO32), (124 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32), (8 bytes).
    Removing dl_rtc_common.o(.text), (0 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_initCalendar), (160 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_initCalendar), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime), (132 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarTime), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1), (100 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1), (88 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1), (68 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1), (76 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2), (100 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2), (88 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2), (68 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2), (76 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm2), (8 bytes).
    Removing dl_opa.o(.text), (0 bytes).
    Removing dl_opa.o(.text.DL_OPA_increaseGain), (52 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_OPA_increaseGain), (8 bytes).
    Removing dl_opa.o(.text.DL_OPA_decreaseGain), (48 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_OPA_decreaseGain), (8 bytes).
    Removing dl_mcan.o(.text), (0 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isReady), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isReady), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setClockConfig), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setClockConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClockConfig), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isInReset), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isInReset), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isFDOpEnable), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isFDOpEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isMemInitDone), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isMemInitDone), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setOpMode), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setOpMode), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getOpMode), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getOpMode), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_init), (236 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_init), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_config), (224 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_config), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccConfig), (184 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setBitTime), (216 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setBitTime), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_msgRAMConfig), (560 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_msgRAMConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setExtIDAndMask), (60 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setExtIDAndMask), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeMsgRam), (228 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsgRam), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_TXBufAddReq), (52 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufAddReq), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getNewDataStatus), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getNewDataStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_clearNewDataStatus), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearNewDataStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_readMsgRam), (252 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsgRam), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_readTxEventFIFO), (96 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_readTxEventFIFO), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addStdMsgIDFilter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addExtMsgIDFilter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_lpbkModeEnable), (88 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_lpbkModeEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getErrCounters), (40 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getErrCounters), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getProtocolStatus), (80 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getProtocolStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_enableIntr), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_selectIntrLine), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_selectIntrLine), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getIntrLineSelectStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrLineSelectStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_enableIntrLine), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntrLine), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getIntrStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_clearIntrStatus), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getHighPriorityMsgStatus), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getHighPriorityMsgStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRxFIFOStatus), (56 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxFIFOStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck), (72 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeRxFIFOAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxFIFOQueStatus), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxFIFOQueStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufReqPend), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufReqPend), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_txBufCancellationReq), (52 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationReq), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufTransmissionStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufTransmissionStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_txBufCancellationStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufTransIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufCancellationIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxEventFIFOStatus), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxEventFIFOStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addClockStopRequest), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addClockStopRequest), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeTxEventFIFOAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccForceError), (220 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccForceError), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus), (128 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetErrorStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus), (112 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearErrorStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccWriteEOI), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWriteEOI), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccEnableIntr), (46 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccEnableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccGetIntrStatus), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccClearIntrStatus), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSCounterConfig), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSCounterEnable), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSEnableIntr), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSEnableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSWriteEOI), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSWriteEOI), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSGetUnservicedIntrCount), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSGetUnservicedIntrCount), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRevisionId), (68 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClockStopAck), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockStopAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSSetRawStatus), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSSetRawStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSClearRawStatus), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSClearRawStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRxPinState), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setTxPinState), (48 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setTxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxPinState), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTSCounterVal), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTSCounterVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClkStopAck), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClkStopAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getBitTime), (76 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getBitTime), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_resetTSCounter), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_resetTSCounter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTOCounterVal), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTOCounterVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccAggrGetRevisionId), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccAggrGetRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId), (84 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWrapGetRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSIsIntrEnable), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSIsIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getEndianVal), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getEndianVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getExtIDANDMask), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getExtIDANDMask), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_saveConfiguration), (300 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_saveConfiguration), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_restoreConfiguration), (348 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_restoreConfiguration), (8 bytes).
    Removing dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize), (64 bytes).
    Removing dl_mcan.o(.rodata.cst32), (32 bytes).
    Removing dl_mathacl.o(.text), (0 bytes).
    Removing dl_mathacl.o(.text.DL_MathACL_configOperation), (40 bytes).
    Removing dl_mathacl.o(.ARM.exidx.text.DL_MathACL_configOperation), (8 bytes).
    Removing dl_lfss.o(.text), (0 bytes).
    Removing dl_lcd.o(.text), (0 bytes).
    Removing dl_keystorectl.o(.text), (0 bytes).
    Removing dl_i2c.o(.text), (0 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_getClockConfig), (26 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig), (8 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO), (52 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO), (132 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO), (68 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO), (52 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking), (36 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck), (24 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck), (8 bytes).
    Removing dl_flashctl.o(.text), (0 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseMemory), (26 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM), (24 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemoryFromRAM), (8 bytes).
    Removing dl_flashctl.o(.ramfunc), (64 bytes).
    Removing dl_flashctl.o(.ARM.exidx.ramfunc), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massErase), (196 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massErase), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory), (16 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectNonMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank), (84 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM), (116 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBankFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank), (352 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseMultiBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryReset), (260 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryReset), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory), (12 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectNonMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM), (144 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank), (100 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetMultiBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8), (30 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCGenerated), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCGenerated), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCGenerated), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCGenerated), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCManual), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCManual), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCManual), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCManual), (60 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated), (148 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectSector), (204 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectSector), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated), (112 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual), (180 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual), (132 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking), (172 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM), (136 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectDataMemory), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectDataMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectMainMemory), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectAllMemory), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectAllMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectAllMemory), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectAllMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectSector), (252 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectSector), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64), (38 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCGenerated), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCGenerated), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCGenerated), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCGenerated), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCManual), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCManual), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCManual), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_blankVerify), (24 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerify), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM), (24 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerifyFromRAM), (8 bytes).
    Removing dl_dma.o(.text), (0 bytes).
    Removing dl_dma.o(.text.DL_DMA_initChannel), (68 bytes).
    Removing dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel), (8 bytes).
    Removing dl_dac12.o(.text), (0 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_init), (140 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_init), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_outputBlocking8), (48 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking8), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_outputBlocking12), (56 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking12), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_fillFIFO8), (160 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO8), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_fillFIFO12), (56 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO12), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_performSelfCalibrationBlocking), (36 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_performSelfCalibrationBlocking), (8 bytes).
    Removing dl_crcp.o(.text), (0 bytes).
    Removing dl_crc.o(.text), (0 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateBlock32), (92 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateMemoryRange32), (52 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateBlock16), (156 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock16), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateMemoryRange16), (104 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange16), (8 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_aesadv.o(.text), (0 bytes).
    Removing dl_aes.o(.text), (0 bytes).
    Removing dl_aes.o(.text.DL_AES_setKey), (72 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_setKey), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_setKeyAligned), (56 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_setKeyAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadDataIn), (40 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadDataIn), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadDataInAligned), (24 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadDataInAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_getDataOut), (36 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_getDataOut), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_getDataOutAligned), (24 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_getDataOutAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataIn), (40 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataIn), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInAligned), (24 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInWithoutTrigger), (40 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTrigger), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInWithoutTriggerAligned), (24 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTriggerAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_xorData), (52 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_xorData), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_xorDataAligned), (36 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_xorDataAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_saveConfiguration), (80 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_saveConfiguration), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_restoreConfiguration), (84 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_restoreConfiguration), (8 bytes).
    Removing dl_adc12.o(.text), (0 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig), (8 bytes).
    Removing dl_adc12.o(.text.DL_ADC12_getClockConfig), (40 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig), (8 bytes).
    Removing dl_interrupt.o(.text), (0 bytes).
    Removing dl_interrupt.o(.text.DL_Interrupt_registerInterrupt), (144 bytes).
    Removing dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_registerInterrupt), (8 bytes).
    Removing dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt), (20 bytes).
    Removing dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_unregisterInterrupt), (8 bytes).
    Removing dl_interrupt.o(.vtable), (192 bytes).
    Removing bsp_debugtimer.o(.text), (0 bytes).
    Removing bsp_debugtimer.o(.ARM.exidx.text.get_StartCount), (8 bytes).
    Removing bsp_debugtimer.o(.ARM.exidx.text.get_Freq), (8 bytes).
    Removing bsp_debugtimer.o(.ARM.exidx.text.get_UsedTime), (8 bytes).
    Removing bsp_iic.o(.text), (0 bytes).
    Removing bsp_iic.o(.text.mspm0_get_clock_ms), (6 bytes).
    Removing bsp_iic.o(.ARM.exidx.text.mspm0_get_clock_ms), (8 bytes).
    Removing bsp_iic.o(.ARM.exidx.text.mpu6050_i2c_sda_unlock), (8 bytes).
    Removing bsp_iic.o(.text.mspm0_i2c_write), (132 bytes).
    Removing bsp_iic.o(.ARM.exidx.text.mspm0_i2c_write), (8 bytes).
    Removing bsp_iic.o(.ARM.exidx.text.mspm0_i2c_read), (8 bytes).
    Removing bsp_iic.o(.ARM.exidx.text.HW_iic_init), (8 bytes).
    Removing bsp_iic.o(.ARM.exidx.text.HW_IIC_Master_Transmit), (8 bytes).
    Removing bsp_iic.o(.ARM.exidx.text.HW_IIC_Master_Receive), (8 bytes).
    Removing bsp_iic.o(.ARM.exidx.text.HW_IIC_Mem_Write), (8 bytes).
    Removing bsp_iic.o(.ARM.exidx.text.HW_IIC_Mem_Read), (8 bytes).
    Removing bsp_iic.o(.ARM.exidx.text.HW_iic_delayms), (8 bytes).
    Removing bsp_key.o(.text), (0 bytes).
    Removing bsp_key.o(.ARM.exidx.text.key_scan), (8 bytes).
    Removing bsp_oled.o(.text), (0 bytes).
    Removing bsp_oled.o(.text.OLED_I2C_Start), (48 bytes).
    Removing bsp_oled.o(.ARM.exidx.text.OLED_I2C_Start), (8 bytes).
    Removing bsp_oled.o(.text.OLED_I2C_Stop), (48 bytes).
    Removing bsp_oled.o(.ARM.exidx.text.OLED_I2C_Stop), (8 bytes).
    Removing bsp_oled.o(.ARM.exidx.text.OLED_I2C_SendByte), (8 bytes).
    Removing bsp_oled.o(.text.OLED_WriteCommand), (92 bytes).
    Removing bsp_oled.o(.ARM.exidx.text.OLED_WriteCommand), (8 bytes).
    Removing bsp_oled.o(.text.OLED_WriteData), (92 bytes).
    Removing bsp_oled.o(.ARM.exidx.text.OLED_WriteData), (8 bytes).
    Removing bsp_oled.o(.ARM.exidx.text.OLED_SetCursor), (8 bytes).
    Removing bsp_oled.o(.ARM.exidx.text.OLED_Clear), (8 bytes).
    Removing bsp_oled.o(.ARM.exidx.text.OLED_ShowChar), (8 bytes).
    Removing bsp_oled.o(.ARM.exidx.text.OLED_ShowString), (8 bytes).
    Removing bsp_oled.o(.text.OLED_Pow), (60 bytes).
    Removing bsp_oled.o(.ARM.exidx.text.OLED_Pow), (8 bytes).
    Removing bsp_oled.o(.text.OLED_ShowNum), (144 bytes).
    Removing bsp_oled.o(.ARM.exidx.text.OLED_ShowNum), (8 bytes).
    Removing bsp_oled.o(.text.OLED_ShowSignedNum), (180 bytes).
    Removing bsp_oled.o(.ARM.exidx.text.OLED_ShowSignedNum), (8 bytes).
    Removing bsp_oled.o(.ARM.exidx.text.OLED_Init), (8 bytes).
    Removing bsp_oled.o(.ARM.exidx.text.OLED_ShowChar_Wrapper), (8 bytes).
    Removing bsp_oled.o(.ARM.exidx.text.OLED_ShowNumber_Wrapper), (8 bytes).
    Removing bsp_oled.o(.ARM.exidx.text.OLED_ShowString_Wrapper), (8 bytes).
    Removing bsp_oled.o(.ARM.exidx.text.OLED_ShowFloat_Wrapper), (8 bytes).
    Removing bsp_oled.o(.ARM.exidx.text.OLED_RefreshGram_Wrapper), (8 bytes).
    Removing bsp_printf.o(.text), (0 bytes).
    Removing bsp_printf.o(.text.any_printf), (100 bytes).
    Removing bsp_printf.o(.ARM.exidx.text.any_printf), (8 bytes).
    Removing bsp_printf.o(.ARM.exidx.text.fputc), (8 bytes).
    Removing bsp_printf.o(.text.fputs), (76 bytes).
    Removing bsp_printf.o(.ARM.exidx.text.fputs), (8 bytes).
    Removing bsp_printf.o(.ARM.exidx.text.puts), (8 bytes).
    Removing bsp_printf.o(.bss.Ux_TxBuff), (256 bytes).
    Removing bsp_systick.o(.text), (0 bytes).
    Removing bsp_systick.o(.ARM.exidx.text.Systick_getTick), (8 bytes).
    Removing bsp_systick.o(.ARM.exidx.text.delay_ms), (8 bytes).
    Removing bsp_systick.o(.ARM.exidx.text.delay_us), (8 bytes).
    Removing inv_mpu.o(.text), (0 bytes).
    Removing inv_mpu.o(.text.myi2cRead), (32 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.myi2cRead), (8 bytes).
    Removing inv_mpu.o(.text.myi2cWrite), (32 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.myi2cWrite), (8 bytes).
    Removing inv_mpu.o(.text.mydelay_ms), (16 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mydelay_ms), (8 bytes).
    Removing inv_mpu.o(.text.set_int_enable), (100 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.set_int_enable), (8 bytes).
    Removing inv_mpu.o(.text.mpu_reg_dump), (92 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_reg_dump), (8 bytes).
    Removing inv_mpu.o(.text.mpu_read_reg), (64 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_read_reg), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_init), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_gyro_fsr), (164 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_gyro_fsr), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_accel_fsr), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_lpf), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_sample_rate), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_configure_fifo), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_bypass), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_sensors), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_lp_accel_mode), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_int_latched), (120 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_int_latched), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_gyro_reg), (84 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_reg), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.myget_ms), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_accel_reg), (80 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_accel_reg), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_temperature), (132 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_temperature), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_accel_bias), (248 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_accel_bias), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_reset_fifo), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_gyro_fsr), (32 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_fsr), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_accel_fsr), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_lpf), (36 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_lpf), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_sample_rate), (28 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_sample_rate), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_compass_sample_rate), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_compass_sample_rate), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_compass_sample_rate), (6 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_compass_sample_rate), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_sens), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_accel_sens), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_fifo_config), (16 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_fifo_config), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_power_state), (20 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_power_state), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_int_status), (68 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_int_status), (8 bytes).
    Removing inv_mpu.o(.text.mpu_read_fifo), (384 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_read_fifo), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_read_fifo_stream), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_int_level), (16 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_int_level), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_run_self_test), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_dmp_state), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.get_st_biases), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_write_mem), (8 bytes).
    Removing inv_mpu.o(.text.mpu_read_mem), (144 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_read_mem), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_load_firmware), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_dmp_state), (16 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_dmp_state), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_compass_reg), (6 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_compass_reg), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_compass_fsr), (6 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_compass_fsr), (8 bytes).
    Removing inv_mpu.o(.text.mpu_lp_motion_interrupt), (956 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_lp_motion_interrupt), (8 bytes).
    Removing inv_mpu.o(.rodata..Lswitch.table.mpu_lp_motion_interrupt.7), (12 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text), (0 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_load_motion_driver_firmware), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_orientation), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_gyro_bias), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_accel_bias), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_fifo_rate), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_get_fifo_rate), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_fifo_rate), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_thresh), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_axes), (44 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_axes), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_count), (36 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_count), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time), (36 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_time), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time_multi), (36 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_time_multi), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_thresh), (46 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_shake_reject_thresh), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_time), (36 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_shake_reject_time), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_timeout), (36 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_shake_reject_timeout), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_get_pedometer_step_count), (44 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_pedometer_step_count), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_step_count), (34 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_pedometer_step_count), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_get_pedometer_walk_time), (48 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_pedometer_walk_time), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_walk_time), (40 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_pedometer_walk_time), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_feature), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_enable_gyro_cal), (68 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_gyro_cal), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_enable_lp_quat), (48 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_lp_quat), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_enable_6x_lp_quat), (48 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_6x_lp_quat), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_get_enabled_features), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_enabled_features), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_interrupt_mode), (84 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_interrupt_mode), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_read_fifo), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_register_tap_cb), (12 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_register_tap_cb), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_register_android_orient_cb), (12 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_register_android_orient_cb), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rodata..L__const.dmp_set_fifo_rate.regs_end), (12 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rodata..L__unnamed_1), (12 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rodata..L__unnamed_2), (12 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rodata.str1.4), (24 bytes).
    Removing kf.o(.text), (0 bytes).
    Removing kf.o(.text.KF_X), (388 bytes).
    Removing kf.o(.ARM.exidx.text.KF_X), (8 bytes).
    Removing kf.o(.text.mul), (424 bytes).
    Removing kf.o(.ARM.exidx.text.mul), (8 bytes).
    Removing kf.o(.text.KF_Y), (388 bytes).
    Removing kf.o(.ARM.exidx.text.KF_Y), (8 bytes).
    Removing kf.o(.bss.KF_X.x_hat.0), (4 bytes).
    Removing kf.o(.bss.KF_X.x_hat.1), (4 bytes).
    Removing kf.o(.data.KF_X.p_hat.0), (4 bytes).
    Removing kf.o(.bss.KF_X.p_hat.1), (4 bytes).
    Removing kf.o(.bss.KF_X.p_hat.2), (4 bytes).
    Removing kf.o(.data.KF_X.p_hat.3), (4 bytes).
    Removing kf.o(.bss.KF_Y.x_hat.0), (4 bytes).
    Removing kf.o(.bss.KF_Y.x_hat.1), (4 bytes).
    Removing kf.o(.data.KF_Y.p_hat.0), (4 bytes).
    Removing kf.o(.bss.KF_Y.p_hat.1), (4 bytes).
    Removing kf.o(.bss.KF_Y.p_hat.2), (4 bytes).
    Removing kf.o(.data.KF_Y.p_hat.3), (4 bytes).
    Removing mpu6050.o(.text), (0 bytes).
    Removing mpu6050.o(.text.IICwriteBits), (108 bytes).
    Removing mpu6050.o(.ARM.exidx.text.IICwriteBits), (8 bytes).
    Removing mpu6050.o(.text.IICwriteBit), (84 bytes).
    Removing mpu6050.o(.ARM.exidx.text.IICwriteBit), (8 bytes).
    Removing mpu6050.o(.text.IICreadBytes), (32 bytes).
    Removing mpu6050.o(.ARM.exidx.text.IICreadBytes), (8 bytes).
    Removing mpu6050.o(.text.i2cRead), (32 bytes).
    Removing mpu6050.o(.ARM.exidx.text.i2cRead), (8 bytes).
    Removing mpu6050.o(.text.I2C_ReadOneByte), (36 bytes).
    Removing mpu6050.o(.ARM.exidx.text.I2C_ReadOneByte), (8 bytes).
    Removing mpu6050.o(.text.MPU6050_newValues), (580 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_newValues), (8 bytes).
    Removing mpu6050.o(.text.MPU6050_setClockSource), (68 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_setClockSource), (8 bytes).
    Removing mpu6050.o(.text.MPU6050_setFullScaleGyroRange), (68 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_setFullScaleGyroRange), (8 bytes).
    Removing mpu6050.o(.text.MPU6050_setFullScaleAccelRange), (68 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_setFullScaleAccelRange), (8 bytes).
    Removing mpu6050.o(.text.MPU6050_setSleepEnabled), (80 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_setSleepEnabled), (8 bytes).
    Removing mpu6050.o(.text.MPU6050_getDeviceID), (40 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_getDeviceID), (8 bytes).
    Removing mpu6050.o(.text.MPU6050_testConnection), (44 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_testConnection), (8 bytes).
    Removing mpu6050.o(.text.MPU6050_setI2CMasterModeEnabled), (80 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_setI2CMasterModeEnabled), (8 bytes).
    Removing mpu6050.o(.text.MPU6050_setI2CBypassEnabled), (80 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_setI2CBypassEnabled), (8 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_initialize), (8 bytes).
    Removing mpu6050.o(.ARM.exidx.text.DMP_Init), (8 bytes).
    Removing mpu6050.o(.ARM.exidx.text.Read_DMP), (8 bytes).
    Removing mpu6050.o(.text.Read_Temperature), (160 bytes).
    Removing mpu6050.o(.ARM.exidx.text.Read_Temperature), (8 bytes).
    Removing mpu6050.o(.bss.RegOri_mpu6050), (36 bytes).
    Removing mpu6050.o(.bss.Gx_offset), (2 bytes).
    Removing mpu6050.o(.bss.Gy_offset), (2 bytes).
    Removing mpu6050.o(.bss.Gz_offset), (2 bytes).
    Removing mpu6050.o(.bss.MPU6050_FIFO), (132 bytes).
    Removing balance.o(.text), (0 bytes).
    Removing balance.o(.ARM.exidx.text.BalanceControlTask), (8 bytes).
    Removing balance.o(.text.Velocity2), (28 bytes).
    Removing balance.o(.ARM.exidx.text.Velocity2), (8 bytes).
    Removing balance.o(.ARM.exidx.text.ADC1_IRQHandler), (8 bytes).
    Removing balance.o(.data.Flag_velocity), (1 bytes).
    Removing balance.o(.bss.Balance_Kp), (4 bytes).
    Removing balance.o(.bss.Balance_Kd), (4 bytes).
    Removing balance.o(.bss.Velocity_Ki), (4 bytes).
    Removing balance.o(.bss.Turn_Kd), (4 bytes).
    Removing balance.o(.data.MiddleAngle), (2 bytes).
    Removing balance.o(.bss.Flag_front), (1 bytes).
    Removing balance.o(.bss.Flag_back), (1 bytes).
    Removing balance.o(.bss.Flag_Left), (1 bytes).
    Removing balance.o(.bss.Flag_Right), (1 bytes).
    Removing bluetooth.o(.text), (0 bytes).
    Removing bluetooth.o(.text.bt_control), (480 bytes).
    Removing bluetooth.o(.ARM.exidx.text.bt_control), (8 bytes).
    Removing bluetooth.o(.bss.gBTCounts), (1 bytes).
    Removing bluetooth.o(.bss.lastBTCounts), (1 bytes).
    Removing bluetooth.o(.bss.PID_Send), (1 bytes).
    Removing bluetooth.o(.bss.bt_control.Flag_PID), (1 bytes).
    Removing bluetooth.o(.bss.bt_control.i), (1 bytes).
    Removing bluetooth.o(.bss.bt_control.j), (1 bytes).
    Removing bluetooth.o(.bss.bt_control.Receive), (50 bytes).
    Removing bluetooth.o(.bss.bt_control.Data), (4 bytes).
    Removing bluetooth.o(.bss.gBTPacket), (200 bytes).
    Removing show.o(.text), (0 bytes).
    Removing show.o(.text.oled_show_test), (108 bytes).
    Removing show.o(.ARM.exidx.text.oled_show_test), (8 bytes).
    Removing show.o(.text.oled_show_simple), (336 bytes).
    Removing show.o(.ARM.exidx.text.oled_show_simple), (8 bytes).
    Removing show.o(.ARM.exidx.text.oled_show), (8 bytes).
    Removing show.o(.bss.oled_show_simple.last_time), (4 bytes).
    Removing show.o(.data.oled_show_simple.last_flag_stop), (1 bytes).
    Removing show.o(.bss.oled_show_simple.init_display), (1 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text), (0 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT), (80 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (60 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (32 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT), (76 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams), (84 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC), (28 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (48 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP), (52 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY), (8 bytes).

915 unused section(s) (total 36905 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/longlong.c                       0x00000000   Number         0  llmul.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  ldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  printfstubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcmp.c         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memmovea.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memmoveb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmple.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmplt.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmpge.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmpgt.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmpeq.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  ffltui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fprnd.c                0x00000000   Number         0  frnd.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  dscalb.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../mathlib/asin.c                        0x00000000   Number         0  asin.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  roundf.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    KF.c                                     0x00000000   Number         0  kf.o ABSOLUTE
    MPU6050.c                                0x00000000   Number         0  mpu6050.o ABSOLUTE
    balance.c                                0x00000000   Number         0  balance.o ABSOLUTE
    bluetooth.c                              0x00000000   Number         0  bluetooth.o ABSOLUTE
    bsp_debugtimer.c                         0x00000000   Number         0  bsp_debugtimer.o ABSOLUTE
    bsp_iic.c                                0x00000000   Number         0  bsp_iic.o ABSOLUTE
    bsp_key.c                                0x00000000   Number         0  bsp_key.o ABSOLUTE
    bsp_oled.c                               0x00000000   Number         0  bsp_oled.o ABSOLUTE
    bsp_printf.c                             0x00000000   Number         0  bsp_printf.o ABSOLUTE
    bsp_systick.c                            0x00000000   Number         0  bsp_systick.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dl_adc12.c                               0x00000000   Number         0  dl_adc12.o ABSOLUTE
    dl_aes.c                                 0x00000000   Number         0  dl_aes.o ABSOLUTE
    dl_aesadv.c                              0x00000000   Number         0  dl_aesadv.o ABSOLUTE
    dl_common.c                              0x00000000   Number         0  dl_common.o ABSOLUTE
    dl_crc.c                                 0x00000000   Number         0  dl_crc.o ABSOLUTE
    dl_crcp.c                                0x00000000   Number         0  dl_crcp.o ABSOLUTE
    dl_dac12.c                               0x00000000   Number         0  dl_dac12.o ABSOLUTE
    dl_dma.c                                 0x00000000   Number         0  dl_dma.o ABSOLUTE
    dl_flashctl.c                            0x00000000   Number         0  dl_flashctl.o ABSOLUTE
    dl_i2c.c                                 0x00000000   Number         0  dl_i2c.o ABSOLUTE
    dl_interrupt.c                           0x00000000   Number         0  dl_interrupt.o ABSOLUTE
    dl_keystorectl.c                         0x00000000   Number         0  dl_keystorectl.o ABSOLUTE
    dl_lcd.c                                 0x00000000   Number         0  dl_lcd.o ABSOLUTE
    dl_lfss.c                                0x00000000   Number         0  dl_lfss.o ABSOLUTE
    dl_mathacl.c                             0x00000000   Number         0  dl_mathacl.o ABSOLUTE
    dl_mcan.c                                0x00000000   Number         0  dl_mcan.o ABSOLUTE
    dl_opa.c                                 0x00000000   Number         0  dl_opa.o ABSOLUTE
    dl_rtc_common.c                          0x00000000   Number         0  dl_rtc_common.o ABSOLUTE
    dl_spi.c                                 0x00000000   Number         0  dl_spi.o ABSOLUTE
    dl_sysctl_mspm0g1x0x_g3x0x.c             0x00000000   Number         0  dl_sysctl_mspm0g1x0x_g3x0x.o ABSOLUTE
    dl_timer.c                               0x00000000   Number         0  dl_timer.o ABSOLUTE
    dl_trng.c                                0x00000000   Number         0  dl_trng.o ABSOLUTE
    dl_uart.c                                0x00000000   Number         0  dl_uart.o ABSOLUTE
    dl_vref.c                                0x00000000   Number         0  dl_vref.o ABSOLUTE
    empty.c                                  0x00000000   Number         0  empty.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    inv_mpu.c                                0x00000000   Number         0  inv_mpu.o ABSOLUTE
    inv_mpu_dmp_motion_driver.c              0x00000000   Number         0  inv_mpu_dmp_motion_driver.o ABSOLUTE
    show.c                                   0x00000000   Number         0  show.o ABSOLUTE
    startup_mspm0g350x_uvision.s             0x00000000   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    RESET                                    0x00000000   Section      192  startup_mspm0g350x_uvision.o(RESET)
    .ARM.Collect$$$$00000000                 0x000000c0   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x000000c0   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x000000c4   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x000000c8   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x000000c8   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x000000c8   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x000000d0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x000000d0   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x000000d0   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x000000d0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x000000d4   Section       20  startup_mspm0g350x_uvision.o(.text)
    .text                                    0x000000e8   Section        0  ldiv.o(.text)
    .text                                    0x00000134   Section        0  llmul.o(.text)
    .text                                    0x00000164   Section        0  memseta.o(.text)
    .text                                    0x00000188   Section        0  strlen.o(.text)
    .text                                    0x00000196   Section        0  memcmp.o(.text)
    .text                                    0x000001b0   Section        0  fadd.o(.text)
    .text                                    0x00000262   Section        0  fmul.o(.text)
    .text                                    0x000002dc   Section        0  fdiv.o(.text)
    .text                                    0x00000358   Section        0  dadd.o(.text)
    .text                                    0x000004bc   Section        0  dmul.o(.text)
    .text                                    0x0000058c   Section        0  ddiv.o(.text)
    .text                                    0x0000067c   Section        0  fcmple.o(.text)
    .text                                    0x00000698   Section        0  fcmplt.o(.text)
    .text                                    0x000006b4   Section        0  fcmpge.o(.text)
    .text                                    0x000006d0   Section        0  fcmpgt.o(.text)
    .text                                    0x000006ec   Section        0  fcmpeq.o(.text)
    .text                                    0x00000708   Section        0  fflti.o(.text)
    .text                                    0x0000071e   Section        0  ffltui.o(.text)
    .text                                    0x0000072c   Section        0  dflti.o(.text)
    .text                                    0x00000754   Section        0  ffixi.o(.text)
    .text                                    0x00000786   Section        0  ffixui.o(.text)
    .text                                    0x000007ae   Section        0  f2d.o(.text)
    .text                                    0x000007d6   Section        0  d2f.o(.text)
    .text                                    0x0000080e   Section        0  uidiv_div0.o(.text)
    .text                                    0x0000084c   Section        0  idiv_div0.o(.text)
    .text                                    0x0000089c   Section        0  uldiv.o(.text)
    .text                                    0x000008fc   Section        0  llshl.o(.text)
    .text                                    0x0000091c   Section        0  llsshr.o(.text)
    .text                                    0x00000942   Section        0  iusefp.o(.text)
    .text                                    0x00000942   Section        0  fepilogue.o(.text)
    .text                                    0x000009c4   Section        0  frnd.o(.text)
    .text                                    0x00000a02   Section        0  depilogue.o(.text)
    .text                                    0x00000ac0   Section        0  dscalb.o(.text)
    .text                                    0x00000aec   Section        0  dfixul.o(.text)
    .text                                    0x00000b2c   Section       40  cdcmple.o(.text)
    .text                                    0x00000b54   Section       40  cdrcmple.o(.text)
    .text                                    0x00000b80   Section       48  init.o(.text)
    .text                                    0x00000bb0   Section        0  llushr.o(.text)
    .text                                    0x00000bd2   Section        0  dsqrt.o(.text)
    [Anonymous Symbol]                       0x00000c74   Section        0  balance.o(.text.ADC1_IRQHandler)
    __arm_cp.2_0                             0x00000c88   Number         4  balance.o(.text.ADC1_IRQHandler)
    [Anonymous Symbol]                       0x00000c8c   Section        0  balance.o(.text.BalanceControlTask)
    __arm_cp.0_0                             0x00000f0c   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_1                             0x00000f10   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_2                             0x00000f14   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_3                             0x00000f18   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_4                             0x00000f1c   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_5                             0x00000f20   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_6                             0x00000f24   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_7                             0x00000f28   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_8                             0x00000f2c   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_9                             0x00000f30   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_10                            0x00000f34   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_11                            0x00000f38   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_12                            0x00000f3c   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_13                            0x00000f40   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_14                            0x00000f44   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_15                            0x00000f48   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_16                            0x00000f4c   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_17                            0x00000f50   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_18                            0x00000f54   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_19                            0x00000f58   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_20                            0x00000f5c   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_21                            0x00000f60   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_22                            0x00000f64   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_23                            0x00000f68   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_24                            0x00000f6c   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_25                            0x00000f70   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_26                            0x00000f74   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_27                            0x00000f78   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_28                            0x00000f7c   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_29                            0x00000f80   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_30                            0x00000f84   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_31                            0x00000f88   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_32                            0x00000f8c   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_33                            0x00000f90   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_34                            0x00000f94   Number         4  balance.o(.text.BalanceControlTask)
    __arm_cp.0_35                            0x00000f98   Number         4  balance.o(.text.BalanceControlTask)
    [Anonymous Symbol]                       0x00000f9c   Section        0  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    __arm_cp.0_0                             0x00000fd4   Number         4  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    __arm_cp.0_1                             0x00000fd8   Number         4  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    [Anonymous Symbol]                       0x00000fdc   Section        0  dl_common.o(.text.DL_Common_delayCycles)
    [Anonymous Symbol]                       0x00000fe6   Section        0  dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO)
    [Anonymous Symbol]                       0x00001068   Section        0  dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO)
    __arm_cp.3_0                             0x000010a8   Number         4  dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO)
    [Anonymous Symbol]                       0x000010ac   Section        0  dl_i2c.o(.text.DL_I2C_setClockConfig)
    [Anonymous Symbol]                       0x000010d4   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_0                             0x00001188   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_2                             0x0000118c   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_3                             0x00001190   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_4                             0x00001194   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    [Anonymous Symbol]                       0x00001198   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_0                             0x000011b8   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_1                             0x000011bc   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    [Anonymous Symbol]                       0x000011c0   Section        0  dl_timer.o(.text.DL_TimerA_initPWMMode)
    __arm_cp.31_0                            0x00001238   Number         4  dl_timer.o(.text.DL_TimerA_initPWMMode)
    __arm_cp.31_1                            0x0000123c   Number         4  dl_timer.o(.text.DL_TimerA_initPWMMode)
    __arm_cp.31_2                            0x00001240   Number         4  dl_timer.o(.text.DL_TimerA_initPWMMode)
    [Anonymous Symbol]                       0x00001244   Section        0  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_0                            0x00001300   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_4                            0x00001304   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_5                            0x00001308   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_6                            0x0000130c   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_7                            0x00001310   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_9                            0x00001314   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    [Anonymous Symbol]                       0x00001318   Section        0  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_0                             0x000013f8   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_2                             0x000013fc   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_3                             0x00001400   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    [Anonymous Symbol]                       0x00001404   Section        0  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    __arm_cp.18_0                            0x0000141c   Number         4  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    [Anonymous Symbol]                       0x00001420   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    __arm_cp.13_0                            0x00001434   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    [Anonymous Symbol]                       0x00001438   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    __arm_cp.3_0                             0x00001444   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    [Anonymous Symbol]                       0x00001448   Section        0  dl_timer.o(.text.DL_Timer_setClockConfig)
    __arm_cp.0_0                             0x00001460   Number         4  dl_timer.o(.text.DL_Timer_setClockConfig)
    [Anonymous Symbol]                       0x00001464   Section        0  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_0                             0x000014a4   Number         4  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_1                             0x000014a8   Number         4  dl_uart.o(.text.DL_UART_init)
    [Anonymous Symbol]                       0x000014ac   Section        0  dl_uart.o(.text.DL_UART_setClockConfig)
    [Anonymous Symbol]                       0x000014c0   Section        0  dl_uart.o(.text.DL_UART_transmitDataBlocking)
    __arm_cp.7_0                             0x000014e0   Number         4  dl_uart.o(.text.DL_UART_transmitDataBlocking)
    [Anonymous Symbol]                       0x000014e4   Section        0  mpu6050.o(.text.DMP_Init)
    __arm_cp.15_0                            0x00001650   Number         4  mpu6050.o(.text.DMP_Init)
    __arm_cp.15_1                            0x00001654   Number         4  mpu6050.o(.text.DMP_Init)
    __arm_cp.15_2                            0x00001658   Number         4  mpu6050.o(.text.DMP_Init)
    __arm_cp.15_3                            0x0000165c   Number         4  mpu6050.o(.text.DMP_Init)
    __arm_cp.15_7                            0x000016e0   Number         4  mpu6050.o(.text.DMP_Init)
    __arm_cp.15_8                            0x000016e4   Number         4  mpu6050.o(.text.DMP_Init)
    __arm_cp.15_9                            0x000016e8   Number         4  mpu6050.o(.text.DMP_Init)
    __arm_cp.15_10                           0x000016ec   Number         4  mpu6050.o(.text.DMP_Init)
    [Anonymous Symbol]                       0x000016f0   Section        0  empty.o(.text.GROUP1_IRQHandler)
    __arm_cp.2_0                             0x0000180c   Number         4  empty.o(.text.GROUP1_IRQHandler)
    __arm_cp.2_1                             0x00001810   Number         4  empty.o(.text.GROUP1_IRQHandler)
    __arm_cp.2_2                             0x00001814   Number         4  empty.o(.text.GROUP1_IRQHandler)
    __arm_cp.2_3                             0x00001818   Number         4  empty.o(.text.GROUP1_IRQHandler)
    __arm_cp.2_4                             0x0000181c   Number         4  empty.o(.text.GROUP1_IRQHandler)
    __arm_cp.2_6                             0x00001820   Number         4  empty.o(.text.GROUP1_IRQHandler)
    __arm_cp.2_7                             0x00001824   Number         4  empty.o(.text.GROUP1_IRQHandler)
    __arm_cp.2_8                             0x00001828   Number         4  empty.o(.text.GROUP1_IRQHandler)
    __arm_cp.2_9                             0x0000182c   Number         4  empty.o(.text.GROUP1_IRQHandler)
    __arm_cp.2_10                            0x00001830   Number         4  empty.o(.text.GROUP1_IRQHandler)
    __arm_cp.2_11                            0x00001834   Number         4  empty.o(.text.GROUP1_IRQHandler)
    __arm_cp.2_12                            0x00001838   Number         4  empty.o(.text.GROUP1_IRQHandler)
    __arm_cp.2_13                            0x0000183c   Number         4  empty.o(.text.GROUP1_IRQHandler)
    __arm_cp.2_14                            0x00001840   Number         4  empty.o(.text.GROUP1_IRQHandler)
    __arm_cp.2_15                            0x00001844   Number         4  empty.o(.text.GROUP1_IRQHandler)
    __arm_cp.2_16                            0x00001848   Number         4  empty.o(.text.GROUP1_IRQHandler)
    __arm_cp.2_17                            0x0000184c   Number         4  empty.o(.text.GROUP1_IRQHandler)
    HW_IIC_Master_Receive                    0x00001851   Thumb Code     4  bsp_iic.o(.text.HW_IIC_Master_Receive)
    [Anonymous Symbol]                       0x00001850   Section        0  bsp_iic.o(.text.HW_IIC_Master_Receive)
    HW_IIC_Master_Transmit                   0x00001855   Thumb Code     4  bsp_iic.o(.text.HW_IIC_Master_Transmit)
    [Anonymous Symbol]                       0x00001854   Section        0  bsp_iic.o(.text.HW_IIC_Master_Transmit)
    HW_IIC_Mem_Read                          0x00001859   Thumb Code    24  bsp_iic.o(.text.HW_IIC_Mem_Read)
    [Anonymous Symbol]                       0x00001858   Section        0  bsp_iic.o(.text.HW_IIC_Mem_Read)
    HW_IIC_Mem_Write                         0x00001871   Thumb Code   112  bsp_iic.o(.text.HW_IIC_Mem_Write)
    [Anonymous Symbol]                       0x00001870   Section        0  bsp_iic.o(.text.HW_IIC_Mem_Write)
    __arm_cp.7_0                             0x000018e0   Number         4  bsp_iic.o(.text.HW_IIC_Mem_Write)
    __arm_cp.7_1                             0x000018e4   Number         4  bsp_iic.o(.text.HW_IIC_Mem_Write)
    __arm_cp.7_2                             0x000018e8   Number         4  bsp_iic.o(.text.HW_IIC_Mem_Write)
    __arm_cp.7_3                             0x000018ec   Number         4  bsp_iic.o(.text.HW_IIC_Mem_Write)
    __arm_cp.7_4                             0x000018f0   Number         4  bsp_iic.o(.text.HW_IIC_Mem_Write)
    __arm_cp.7_5                             0x000018f4   Number         4  bsp_iic.o(.text.HW_IIC_Mem_Write)
    HW_iic_delayms                           0x000018f9   Thumb Code     8  bsp_iic.o(.text.HW_iic_delayms)
    [Anonymous Symbol]                       0x000018f8   Section        0  bsp_iic.o(.text.HW_iic_delayms)
    HW_iic_init                              0x00001901   Thumb Code   120  bsp_iic.o(.text.HW_iic_init)
    [Anonymous Symbol]                       0x00001900   Section        0  bsp_iic.o(.text.HW_iic_init)
    __arm_cp.4_0                             0x00001978   Number         4  bsp_iic.o(.text.HW_iic_init)
    __arm_cp.4_1                             0x0000197c   Number         4  bsp_iic.o(.text.HW_iic_init)
    __arm_cp.4_2                             0x00001980   Number         4  bsp_iic.o(.text.HW_iic_init)
    __arm_cp.4_3                             0x00001984   Number         4  bsp_iic.o(.text.HW_iic_init)
    __arm_cp.4_4                             0x00001988   Number         4  bsp_iic.o(.text.HW_iic_init)
    __arm_cp.4_5                             0x0000198c   Number         4  bsp_iic.o(.text.HW_iic_init)
    __arm_cp.4_6                             0x00001990   Number         4  bsp_iic.o(.text.HW_iic_init)
    __arm_cp.4_7                             0x00001994   Number         4  bsp_iic.o(.text.HW_iic_init)
    [Anonymous Symbol]                       0x00001998   Section        0  empty.o(.text.K210_Receive_Data)
    __arm_cp.4_0                             0x00001ac8   Number         4  empty.o(.text.K210_Receive_Data)
    __arm_cp.4_1                             0x00001acc   Number         4  empty.o(.text.K210_Receive_Data)
    __arm_cp.4_2                             0x00001ad0   Number         4  empty.o(.text.K210_Receive_Data)
    __arm_cp.4_3                             0x00001ad4   Number         4  empty.o(.text.K210_Receive_Data)
    [Anonymous Symbol]                       0x00001ad8   Section        0  mpu6050.o(.text.MPU6050_initialize)
    __arm_cp.14_0                            0x00001c20   Number         4  mpu6050.o(.text.MPU6050_initialize)
    __arm_cp.14_1                            0x00001c24   Number         4  mpu6050.o(.text.MPU6050_initialize)
    __arm_cp.14_2                            0x00001c28   Number         4  mpu6050.o(.text.MPU6050_initialize)
    __arm_cp.14_3                            0x00001c2c   Number         4  mpu6050.o(.text.MPU6050_initialize)
    [Anonymous Symbol]                       0x00001c30   Section        0  bsp_oled.o(.text.OLED_Clear)
    [Anonymous Symbol]                       0x00001cb4   Section        0  bsp_oled.o(.text.OLED_I2C_SendByte)
    __arm_cp.2_0                             0x00001df4   Number         4  bsp_oled.o(.text.OLED_I2C_SendByte)
    [Anonymous Symbol]                       0x00001df8   Section        0  bsp_oled.o(.text.OLED_Init)
    __arm_cp.12_1                            0x000021f8   Number         4  bsp_oled.o(.text.OLED_Init)
    OLED_RefreshGram_Wrapper                 0x000025b5   Thumb Code     2  bsp_oled.o(.text.OLED_RefreshGram_Wrapper)
    [Anonymous Symbol]                       0x000025b4   Section        0  bsp_oled.o(.text.OLED_RefreshGram_Wrapper)
    [Anonymous Symbol]                       0x000025b8   Section        0  bsp_oled.o(.text.OLED_SetCursor)
    [Anonymous Symbol]                       0x000026ac   Section        0  bsp_oled.o(.text.OLED_ShowChar)
    __arm_cp.7_0                             0x000027bc   Number         4  bsp_oled.o(.text.OLED_ShowChar)
    __arm_cp.7_1                             0x000027c0   Number         4  bsp_oled.o(.text.OLED_ShowChar)
    __arm_cp.7_2                             0x000027c4   Number         4  bsp_oled.o(.text.OLED_ShowChar)
    OLED_ShowChar_Wrapper                    0x000027c9   Thumb Code    30  bsp_oled.o(.text.OLED_ShowChar_Wrapper)
    [Anonymous Symbol]                       0x000027c8   Section        0  bsp_oled.o(.text.OLED_ShowChar_Wrapper)
    OLED_ShowFloat_Wrapper                   0x000027e9   Thumb Code   404  bsp_oled.o(.text.OLED_ShowFloat_Wrapper)
    [Anonymous Symbol]                       0x000027e8   Section        0  bsp_oled.o(.text.OLED_ShowFloat_Wrapper)
    __arm_cp.16_0                            0x0000297c   Number         4  bsp_oled.o(.text.OLED_ShowFloat_Wrapper)
    OLED_ShowNumber_Wrapper                  0x00002981   Thumb Code   164  bsp_oled.o(.text.OLED_ShowNumber_Wrapper)
    [Anonymous Symbol]                       0x00002980   Section        0  bsp_oled.o(.text.OLED_ShowNumber_Wrapper)
    __arm_cp.14_0                            0x00002a24   Number         4  bsp_oled.o(.text.OLED_ShowNumber_Wrapper)
    [Anonymous Symbol]                       0x00002a28   Section        0  bsp_oled.o(.text.OLED_ShowString)
    OLED_ShowString_Wrapper                  0x00002a55   Thumb Code    60  bsp_oled.o(.text.OLED_ShowString_Wrapper)
    [Anonymous Symbol]                       0x00002a54   Section        0  bsp_oled.o(.text.OLED_ShowString_Wrapper)
    [Anonymous Symbol]                       0x00002a90   Section        0  mpu6050.o(.text.Read_DMP)
    __arm_cp.16_0                            0x00002c5c   Number         4  mpu6050.o(.text.Read_DMP)
    __arm_cp.16_1                            0x00002c60   Number         4  mpu6050.o(.text.Read_DMP)
    __arm_cp.16_2                            0x00002c64   Number         4  mpu6050.o(.text.Read_DMP)
    __arm_cp.16_3                            0x00002c68   Number         4  mpu6050.o(.text.Read_DMP)
    __arm_cp.16_4                            0x00002c6c   Number         4  mpu6050.o(.text.Read_DMP)
    __arm_cp.16_5                            0x00002c70   Number         4  mpu6050.o(.text.Read_DMP)
    __arm_cp.16_6                            0x00002c74   Number         4  mpu6050.o(.text.Read_DMP)
    __arm_cp.16_7                            0x00002c78   Number         4  mpu6050.o(.text.Read_DMP)
    __arm_cp.16_8                            0x00002c7c   Number         4  mpu6050.o(.text.Read_DMP)
    __arm_cp.16_9                            0x00002c80   Number         4  mpu6050.o(.text.Read_DMP)
    __arm_cp.16_10                           0x00002c84   Number         4  mpu6050.o(.text.Read_DMP)
    __arm_cp.16_11                           0x00002c88   Number         4  mpu6050.o(.text.Read_DMP)
    [Anonymous Symbol]                       0x00002c8c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init)
    __arm_cp.10_0                            0x00002cc8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init)
    __arm_cp.10_1                            0x00002ccc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init)
    __arm_cp.10_2                            0x00002cd0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init)
    __arm_cp.10_3                            0x00002cd4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init)
    __arm_cp.10_4                            0x00002cd8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init)
    [Anonymous Symbol]                       0x00002cdc   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_DebugTimer_init)
    __arm_cp.6_0                             0x00002cf8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_DebugTimer_init)
    __arm_cp.6_1                             0x00002cfc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_DebugTimer_init)
    __arm_cp.6_2                             0x00002d00   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_DebugTimer_init)
    __arm_cp.6_3                             0x00002d04   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_DebugTimer_init)
    [Anonymous Symbol]                       0x00002d08   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_0                             0x00002dd8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_1                             0x00002ddc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_2                             0x00002de0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_3                             0x00002de4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_4                             0x00002de8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_5                             0x00002dec   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_6                             0x00002df0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_7                             0x00002df4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_8                             0x00002df8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_9                             0x00002dfc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_10                            0x00002e00   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_11                            0x00002e04   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_12                            0x00002e08   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_13                            0x00002e0c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x00002e10   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    __arm_cp.7_0                             0x00002e58   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    __arm_cp.7_1                             0x00002e5c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    __arm_cp.7_2                             0x00002e60   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    [Anonymous Symbol]                       0x00002e64   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.4_0                             0x00002ed8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.4_1                             0x00002edc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.4_2                             0x00002ee0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.4_3                             0x00002ee4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.4_4                             0x00002ee8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.4_5                             0x00002eec   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    [Anonymous Symbol]                       0x00002ef0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_0                             0x00002f64   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_1                             0x00002f68   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_2                             0x00002f6c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_3                             0x00002f70   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x00002f74   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    __arm_cp.11_0                            0x00002f8c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    __arm_cp.11_1                            0x00002f90   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    [Anonymous Symbol]                       0x00002f94   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.5_0                             0x00002fb8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.5_1                             0x00002fbc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.5_2                             0x00002fc0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.5_3                             0x00002fc4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.5_4                             0x00002fc8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    [Anonymous Symbol]                       0x00002fcc   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.8_0                             0x00003028   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.8_1                             0x0000302c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.8_2                             0x00003030   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.8_3                             0x00003034   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.8_5                             0x00003038   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    [Anonymous Symbol]                       0x0000303c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    __arm_cp.9_0                             0x000030a4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    __arm_cp.9_1                             0x000030a8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    __arm_cp.9_2                             0x000030ac   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    __arm_cp.9_3                             0x000030b0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    __arm_cp.9_4                             0x000030b4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    __arm_cp.9_5                             0x000030b8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    __arm_cp.9_6                             0x000030bc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    [Anonymous Symbol]                       0x000030c0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_0                             0x00003100   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_1                             0x00003104   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x00003108   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_0                             0x00003158   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_1                             0x0000315c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_2                             0x00003160   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_3                             0x00003164   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_4                             0x00003168   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_5                             0x0000316c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_6                             0x00003170   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_7                             0x00003174   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_8                             0x00003178   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_9                             0x0000317c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_10                            0x00003180   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    [Anonymous Symbol]                       0x00003184   Section        0  bsp_systick.o(.text.Systick_getTick)
    [Anonymous Symbol]                       0x0000318c   Section        0  empty.o(.text.UART0_IRQHandler)
    __arm_cp.3_0                             0x000031a0   Number         4  empty.o(.text.UART0_IRQHandler)
    __arm_cp.3_1                             0x000031a4   Number         4  empty.o(.text.UART0_IRQHandler)
    [Anonymous Symbol]                       0x000031a8   Section        0  empty.o(.text.UART2_IRQHandler)
    __arm_cp.5_0                             0x000031c8   Number         4  empty.o(.text.UART2_IRQHandler)
    __arm_cp.5_1                             0x000031cc   Number         4  empty.o(.text.UART2_IRQHandler)
    __arm_cp.5_2                             0x000031d0   Number         4  empty.o(.text.UART2_IRQHandler)
    [Anonymous Symbol]                       0x000031d4   Section        0  bsp_systick.o(.text.delay_ms)
    __arm_cp.1_2                             0x00003248   Number         4  bsp_systick.o(.text.delay_ms)
    [Anonymous Symbol]                       0x0000324c   Section        0  bsp_systick.o(.text.delay_us)
    __arm_cp.2_0                             0x000032c4   Number         4  bsp_systick.o(.text.delay_us)
    __arm_cp.2_1                             0x000032c8   Number         4  bsp_systick.o(.text.delay_us)
    __arm_cp.2_2                             0x000032cc   Number         4  bsp_systick.o(.text.delay_us)
    [Anonymous Symbol]                       0x000032d0   Section        0  inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature)
    __arm_cp.18_0                            0x00003554   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature)
    __arm_cp.18_1                            0x00003558   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature)
    __arm_cp.18_2                            0x0000355c   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature)
    __arm_cp.18_3                            0x00003560   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature)
    __arm_cp.18_4                            0x00003564   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature)
    __arm_cp.18_5                            0x00003568   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature)
    __arm_cp.18_6                            0x0000356c   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature)
    __arm_cp.18_7                            0x00003570   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature)
    [Anonymous Symbol]                       0x00003574   Section        0  inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware)
    __arm_cp.0_0                             0x00003588   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware)
    __arm_cp.0_1                             0x0000358c   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware)
    [Anonymous Symbol]                       0x00003590   Section        0  inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo)
    __arm_cp.24_0                            0x00003724   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo)
    __arm_cp.24_1                            0x00003728   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo)
    __arm_cp.24_2                            0x0000372c   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo)
    __arm_cp.24_3                            0x00003730   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo)
    [Anonymous Symbol]                       0x00003734   Section        0  inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias)
    [Anonymous Symbol]                       0x000037f8   Section        0  inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate)
    __arm_cp.4_0                             0x0000384c   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate)
    __arm_cp.4_1                             0x00003850   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate)
    __arm_cp.4_2                             0x00003854   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate)
    __arm_cp.4_3                             0x00003858   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate)
    __arm_cp.4_4                             0x0000385c   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate)
    __arm_cp.4_5                             0x00003860   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate)
    [Anonymous Symbol]                       0x00003864   Section        0  inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias)
    __arm_cp.2_1                             0x00003948   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias)
    [Anonymous Symbol]                       0x0000394c   Section        0  inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation)
    __arm_cp.1_0                             0x00003a0c   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation)
    __arm_cp.1_1                             0x00003a10   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation)
    __arm_cp.1_2                             0x00003a14   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation)
    __arm_cp.1_3                             0x00003a18   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation)
    __arm_cp.1_4                             0x00003a1c   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation)
    __arm_cp.1_5                             0x00003a20   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation)
    [Anonymous Symbol]                       0x00003a24   Section        0  inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh)
    __arm_cp.6_0                             0x00003b44   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh)
    __arm_cp.6_1                             0x00003b48   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh)
    __arm_cp.6_2                             0x00003b4c   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh)
    __arm_cp.6_3                             0x00003b50   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh)
    __arm_cp.6_4                             0x00003b54   Number         4  inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh)
    [Anonymous Symbol]                       0x00003b58   Section        0  bsp_printf.o(.text.fputc)
    __arm_cp.1_0                             0x00003b84   Number         4  bsp_printf.o(.text.fputc)
    __arm_cp.1_1                             0x00003b88   Number         4  bsp_printf.o(.text.fputc)
    get_Freq                                 0x00003b8d   Thumb Code    76  bsp_debugtimer.o(.text.get_Freq)
    [Anonymous Symbol]                       0x00003b8c   Section        0  bsp_debugtimer.o(.text.get_Freq)
    get_StartCount                           0x00003bd9   Thumb Code     8  bsp_debugtimer.o(.text.get_StartCount)
    [Anonymous Symbol]                       0x00003bd8   Section        0  bsp_debugtimer.o(.text.get_StartCount)
    get_UsedTime                             0x00003be1   Thumb Code    44  bsp_debugtimer.o(.text.get_UsedTime)
    [Anonymous Symbol]                       0x00003be0   Section        0  bsp_debugtimer.o(.text.get_UsedTime)
    __arm_cp.2_0                             0x00003c0c   Number         4  bsp_debugtimer.o(.text.get_UsedTime)
    __arm_cp.2_1                             0x00003c10   Number         4  bsp_debugtimer.o(.text.get_UsedTime)
    __arm_cp.2_2                             0x00003c14   Number         4  bsp_debugtimer.o(.text.get_UsedTime)
    __arm_cp.2_3                             0x00003c18   Number         4  bsp_debugtimer.o(.text.get_UsedTime)
    get_st_biases                            0x00003c1d   Thumb Code   924  inv_mpu.o(.text.get_st_biases)
    [Anonymous Symbol]                       0x00003c1c   Section        0  inv_mpu.o(.text.get_st_biases)
    __arm_cp.38_0                            0x00003fb8   Number         4  inv_mpu.o(.text.get_st_biases)
    __arm_cp.38_1                            0x00003fbc   Number         4  inv_mpu.o(.text.get_st_biases)
    __arm_cp.38_2                            0x00003fc0   Number         4  inv_mpu.o(.text.get_st_biases)
    __arm_cp.38_3                            0x00003fc4   Number         4  inv_mpu.o(.text.get_st_biases)
    key_scan                                 0x00003fc9   Thumb Code   220  bsp_key.o(.text.key_scan)
    [Anonymous Symbol]                       0x00003fc8   Section        0  bsp_key.o(.text.key_scan)
    __arm_cp.0_0                             0x000040a4   Number         4  bsp_key.o(.text.key_scan)
    __arm_cp.0_1                             0x000040a8   Number         4  bsp_key.o(.text.key_scan)
    __arm_cp.0_2                             0x000040ac   Number         4  bsp_key.o(.text.key_scan)
    __arm_cp.0_3                             0x000040b0   Number         4  bsp_key.o(.text.key_scan)
    __arm_cp.0_4                             0x000040b4   Number         4  bsp_key.o(.text.key_scan)
    __arm_cp.0_5                             0x000040b8   Number         4  bsp_key.o(.text.key_scan)
    __arm_cp.0_6                             0x000040bc   Number         4  bsp_key.o(.text.key_scan)
    __arm_cp.0_7                             0x000040c0   Number         4  bsp_key.o(.text.key_scan)
    __arm_cp.0_8                             0x000040c4   Number         4  bsp_key.o(.text.key_scan)
    [Anonymous Symbol]                       0x000040c8   Section        0  empty.o(.text.main)
    __arm_cp.0_0                             0x00004350   Number         4  empty.o(.text.main)
    __arm_cp.0_1                             0x00004354   Number         4  empty.o(.text.main)
    __arm_cp.0_2                             0x00004358   Number         4  empty.o(.text.main)
    __arm_cp.0_4                             0x00004368   Number         4  empty.o(.text.main)
    __arm_cp.0_5                             0x0000436c   Number         4  empty.o(.text.main)
    __arm_cp.0_6                             0x00004370   Number         4  empty.o(.text.main)
    __arm_cp.0_7                             0x00004374   Number         4  empty.o(.text.main)
    __arm_cp.0_17                            0x000043f0   Number         4  empty.o(.text.main)
    __arm_cp.0_18                            0x000043f4   Number         4  empty.o(.text.main)
    __arm_cp.0_19                            0x000043f8   Number         4  empty.o(.text.main)
    __arm_cp.0_20                            0x000043fc   Number         4  empty.o(.text.main)
    __arm_cp.0_21                            0x00004400   Number         4  empty.o(.text.main)
    __arm_cp.0_22                            0x00004404   Number         4  empty.o(.text.main)
    __arm_cp.0_23                            0x00004408   Number         4  empty.o(.text.main)
    __arm_cp.0_24                            0x0000440c   Number         4  empty.o(.text.main)
    __arm_cp.0_25                            0x00004410   Number         4  empty.o(.text.main)
    __arm_cp.0_26                            0x00004414   Number         4  empty.o(.text.main)
    __arm_cp.0_27                            0x00004418   Number         4  empty.o(.text.main)
    __arm_cp.0_28                            0x0000441c   Number         4  empty.o(.text.main)
    __arm_cp.0_29                            0x00004420   Number         4  empty.o(.text.main)
    __arm_cp.0_30                            0x00004424   Number         4  empty.o(.text.main)
    __arm_cp.0_31                            0x00004428   Number         4  empty.o(.text.main)
    __arm_cp.0_32                            0x0000442c   Number         4  empty.o(.text.main)
    __arm_cp.0_33                            0x00004430   Number         4  empty.o(.text.main)
    __arm_cp.0_34                            0x00004434   Number         4  empty.o(.text.main)
    __arm_cp.0_35                            0x00004438   Number         4  empty.o(.text.main)
    __arm_cp.0_36                            0x0000443c   Number         4  empty.o(.text.main)
    __arm_cp.0_37                            0x00004440   Number         4  empty.o(.text.main)
    __arm_cp.0_38                            0x00004444   Number         4  empty.o(.text.main)
    __arm_cp.0_39                            0x00004448   Number         4  empty.o(.text.main)
    __arm_cp.0_40                            0x0000444c   Number         4  empty.o(.text.main)
    [Anonymous Symbol]                       0x00004450   Section        0  bsp_iic.o(.text.mpu6050_i2c_sda_unlock)
    __arm_cp.1_0                             0x000044c0   Number         4  bsp_iic.o(.text.mpu6050_i2c_sda_unlock)
    __arm_cp.1_1                             0x000044c4   Number         4  bsp_iic.o(.text.mpu6050_i2c_sda_unlock)
    __arm_cp.1_2                             0x000044c8   Number         4  bsp_iic.o(.text.mpu6050_i2c_sda_unlock)
    __arm_cp.1_3                             0x000044cc   Number         4  bsp_iic.o(.text.mpu6050_i2c_sda_unlock)
    __arm_cp.1_4                             0x000044d0   Number         4  bsp_iic.o(.text.mpu6050_i2c_sda_unlock)
    __arm_cp.1_5                             0x000044d4   Number         4  bsp_iic.o(.text.mpu6050_i2c_sda_unlock)
    __arm_cp.1_6                             0x000044d8   Number         4  bsp_iic.o(.text.mpu6050_i2c_sda_unlock)
    [Anonymous Symbol]                       0x000044dc   Section        0  inv_mpu.o(.text.mpu_configure_fifo)
    [Anonymous Symbol]                       0x00004598   Section        0  inv_mpu.o(.text.mpu_get_accel_fsr)
    __arm_cp.23_1                            0x000045c0   Number         4  inv_mpu.o(.text.mpu_get_accel_fsr)
    __arm_cp.23_2                            0x000045c4   Number         4  inv_mpu.o(.text.mpu_get_accel_fsr)
    [Anonymous Symbol]                       0x000045c8   Section        0  inv_mpu.o(.text.mpu_get_accel_sens)
    __arm_cp.29_0                            0x000045f0   Number         4  inv_mpu.o(.text.mpu_get_accel_sens)
    [Anonymous Symbol]                       0x00004604   Section        0  inv_mpu.o(.text.mpu_get_gyro_sens)
    __arm_cp.28_1                            0x00004638   Number         4  inv_mpu.o(.text.mpu_get_gyro_sens)
    __arm_cp.28_2                            0x0000463c   Number         4  inv_mpu.o(.text.mpu_get_gyro_sens)
    __arm_cp.28_3                            0x00004640   Number         4  inv_mpu.o(.text.mpu_get_gyro_sens)
    __arm_cp.28_4                            0x00004644   Number         4  inv_mpu.o(.text.mpu_get_gyro_sens)
    [Anonymous Symbol]                       0x00004648   Section        0  inv_mpu.o(.text.mpu_init)
    __arm_cp.6_0                             0x000047e8   Number         4  inv_mpu.o(.text.mpu_init)
    __arm_cp.6_1                             0x000047ec   Number         4  inv_mpu.o(.text.mpu_init)
    __arm_cp.6_3                             0x00004810   Number         4  inv_mpu.o(.text.mpu_init)
    __arm_cp.6_4                             0x00004814   Number         4  inv_mpu.o(.text.mpu_init)
    [Anonymous Symbol]                       0x00004840   Section        0  inv_mpu.o(.text.mpu_load_firmware)
    __arm_cp.41_0                            0x000049ac   Number         4  inv_mpu.o(.text.mpu_load_firmware)
    [Anonymous Symbol]                       0x000049b0   Section        0  inv_mpu.o(.text.mpu_lp_accel_mode)
    __arm_cp.14_0                            0x00004c08   Number         4  inv_mpu.o(.text.mpu_lp_accel_mode)
    __arm_cp.14_1                            0x00004c0c   Number         4  inv_mpu.o(.text.mpu_lp_accel_mode)
    [Anonymous Symbol]                       0x00004c10   Section        0  inv_mpu.o(.text.mpu_read_fifo_stream)
    __arm_cp.34_0                            0x00004ce0   Number         4  inv_mpu.o(.text.mpu_read_fifo_stream)
    __arm_cp.34_1                            0x00004ce4   Number         4  inv_mpu.o(.text.mpu_read_fifo_stream)
    [Anonymous Symbol]                       0x00004ce8   Section        0  inv_mpu.o(.text.mpu_reset_fifo)
    __arm_cp.21_0                            0x00004e84   Number         4  inv_mpu.o(.text.mpu_reset_fifo)
    __arm_cp.21_1                            0x00004e88   Number         4  inv_mpu.o(.text.mpu_reset_fifo)
    [Anonymous Symbol]                       0x00004e8c   Section        0  inv_mpu.o(.text.mpu_run_self_test)
    __arm_cp.36_25                           0x0000527c   Number         4  inv_mpu.o(.text.mpu_run_self_test)
    __arm_cp.36_29                           0x000052a4   Number         4  inv_mpu.o(.text.mpu_run_self_test)
    __arm_cp.36_30                           0x000052a8   Number         4  inv_mpu.o(.text.mpu_run_self_test)
    __arm_cp.36_31                           0x000052ac   Number         4  inv_mpu.o(.text.mpu_run_self_test)
    __arm_cp.36_32                           0x000052b0   Number         4  inv_mpu.o(.text.mpu_run_self_test)
    __arm_cp.36_33                           0x000052b4   Number         4  inv_mpu.o(.text.mpu_run_self_test)
    __arm_cp.36_36                           0x000052c0   Number         4  inv_mpu.o(.text.mpu_run_self_test)
    __arm_cp.36_37                           0x000052c4   Number         4  inv_mpu.o(.text.mpu_run_self_test)
    __arm_cp.36_12                           0x000056b8   Number         4  inv_mpu.o(.text.mpu_run_self_test)
    __arm_cp.36_13                           0x000056bc   Number         4  inv_mpu.o(.text.mpu_run_self_test)
    __arm_cp.36_14                           0x000056c0   Number         4  inv_mpu.o(.text.mpu_run_self_test)
    __arm_cp.36_15                           0x000056c4   Number         4  inv_mpu.o(.text.mpu_run_self_test)
    __arm_cp.36_16                           0x000056c8   Number         4  inv_mpu.o(.text.mpu_run_self_test)
    __arm_cp.36_17                           0x000056cc   Number         4  inv_mpu.o(.text.mpu_run_self_test)
    __arm_cp.36_18                           0x000056d0   Number         4  inv_mpu.o(.text.mpu_run_self_test)
    __arm_cp.36_19                           0x000056d4   Number         4  inv_mpu.o(.text.mpu_run_self_test)
    __arm_cp.36_20                           0x000056d8   Number         4  inv_mpu.o(.text.mpu_run_self_test)
    __arm_cp.36_21                           0x000056dc   Number         4  inv_mpu.o(.text.mpu_run_self_test)
    __arm_cp.36_22                           0x000056e0   Number         4  inv_mpu.o(.text.mpu_run_self_test)
    [Anonymous Symbol]                       0x000056e4   Section        0  inv_mpu.o(.text.mpu_set_accel_fsr)
    [Anonymous Symbol]                       0x00005774   Section        0  inv_mpu.o(.text.mpu_set_bypass)
    __arm_cp.12_1                            0x00005898   Number         4  inv_mpu.o(.text.mpu_set_bypass)
    [Anonymous Symbol]                       0x0000589c   Section        0  inv_mpu.o(.text.mpu_set_dmp_state)
    __arm_cp.37_1                            0x00005998   Number         4  inv_mpu.o(.text.mpu_set_dmp_state)
    [Anonymous Symbol]                       0x0000599c   Section        0  inv_mpu.o(.text.mpu_set_lpf)
    __arm_cp.9_0                             0x00005a20   Number         4  inv_mpu.o(.text.mpu_set_lpf)
    __arm_cp.9_1                             0x00005a24   Number         4  inv_mpu.o(.text.mpu_set_lpf)
    [Anonymous Symbol]                       0x00005a28   Section        0  inv_mpu.o(.text.mpu_set_sample_rate)
    __arm_cp.10_0                            0x00005b18   Number         4  inv_mpu.o(.text.mpu_set_sample_rate)
    __arm_cp.10_1                            0x00005b1c   Number         4  inv_mpu.o(.text.mpu_set_sample_rate)
    [Anonymous Symbol]                       0x00005b20   Section        0  inv_mpu.o(.text.mpu_set_sensors)
    __arm_cp.13_0                            0x00005c14   Number         4  inv_mpu.o(.text.mpu_set_sensors)
    [Anonymous Symbol]                       0x00005c18   Section        0  inv_mpu.o(.text.mpu_write_mem)
    __arm_cp.39_0                            0x00005c94   Number         4  inv_mpu.o(.text.mpu_write_mem)
    __arm_cp.39_1                            0x00005c98   Number         4  inv_mpu.o(.text.mpu_write_mem)
    [Anonymous Symbol]                       0x00005c9c   Section        0  bsp_iic.o(.text.mspm0_i2c_read)
    __arm_cp.3_0                             0x00005d80   Number         4  bsp_iic.o(.text.mspm0_i2c_read)
    __arm_cp.3_1                             0x00005d84   Number         4  bsp_iic.o(.text.mspm0_i2c_read)
    __arm_cp.3_2                             0x00005d88   Number         4  bsp_iic.o(.text.mspm0_i2c_read)
    __arm_cp.3_3                             0x00005d8c   Number         4  bsp_iic.o(.text.mspm0_i2c_read)
    __arm_cp.3_4                             0x00005d90   Number         4  bsp_iic.o(.text.mspm0_i2c_read)
    [Anonymous Symbol]                       0x00005d94   Section        0  inv_mpu.o(.text.myget_ms)
    [Anonymous Symbol]                       0x00005d98   Section        0  show.o(.text.oled_show)
    __arm_cp.2_0                             0x000060a4   Number         4  show.o(.text.oled_show)
    __arm_cp.2_1                             0x000060a8   Number         4  show.o(.text.oled_show)
    __arm_cp.2_2                             0x000060ac   Number         4  show.o(.text.oled_show)
    __arm_cp.2_3                             0x000060b0   Number         4  show.o(.text.oled_show)
    __arm_cp.2_4                             0x000060b4   Number         4  show.o(.text.oled_show)
    __arm_cp.2_5                             0x000060b8   Number         4  show.o(.text.oled_show)
    __arm_cp.2_7                             0x000060c0   Number         4  show.o(.text.oled_show)
    __arm_cp.2_8                             0x000060c4   Number         4  show.o(.text.oled_show)
    __arm_cp.2_10                            0x000060cc   Number         4  show.o(.text.oled_show)
    __arm_cp.2_12                            0x000060d4   Number         4  show.o(.text.oled_show)
    __arm_cp.2_13                            0x000060d8   Number         4  show.o(.text.oled_show)
    __arm_cp.2_14                            0x000060dc   Number         4  show.o(.text.oled_show)
    __arm_cp.2_15                            0x000060e0   Number         4  show.o(.text.oled_show)
    __arm_cp.2_16                            0x000060e4   Number         4  show.o(.text.oled_show)
    __arm_cp.2_17                            0x000060e8   Number         4  show.o(.text.oled_show)
    __arm_cp.2_18                            0x000060ec   Number         4  show.o(.text.oled_show)
    __arm_cp.2_19                            0x000060f0   Number         4  show.o(.text.oled_show)
    __arm_cp.2_20                            0x000060f4   Number         4  show.o(.text.oled_show)
    __arm_cp.2_21                            0x000060f8   Number         4  show.o(.text.oled_show)
    __arm_cp.2_22                            0x000060fc   Number         4  show.o(.text.oled_show)
    __arm_cp.2_24                            0x00006104   Number         4  show.o(.text.oled_show)
    __arm_cp.2_25                            0x00006108   Number         4  show.o(.text.oled_show)
    __arm_cp.2_26                            0x0000610c   Number         4  show.o(.text.oled_show)
    __arm_cp.2_27                            0x00006110   Number         4  show.o(.text.oled_show)
    __arm_cp.2_28                            0x00006114   Number         4  show.o(.text.oled_show)
    __arm_cp.2_29                            0x00006118   Number         4  show.o(.text.oled_show)
    __arm_cp.2_31                            0x00006120   Number         4  show.o(.text.oled_show)
    __arm_cp.2_32                            0x00006124   Number         4  show.o(.text.oled_show)
    __arm_cp.2_33                            0x00006128   Number         4  show.o(.text.oled_show)
    __arm_cp.2_35                            0x00006130   Number         4  show.o(.text.oled_show)
    [Anonymous Symbol]                       0x0000613c   Section        0  bsp_printf.o(.text.puts)
    __arm_cp.3_0                             0x000061a0   Number         4  bsp_printf.o(.text.puts)
    __arm_cp.3_1                             0x000061a4   Number         4  bsp_printf.o(.text.puts)
    [Anonymous Symbol]                       0x000061a8   Section        0  empty.o(.text.xunji)
    __arm_cp.1_0                             0x000062d0   Number         4  empty.o(.text.xunji)
    __arm_cp.1_1                             0x000062d4   Number         4  empty.o(.text.xunji)
    __arm_cp.1_2                             0x000062d8   Number         4  empty.o(.text.xunji)
    __arm_cp.1_3                             0x000062dc   Number         4  empty.o(.text.xunji)
    __arm_cp.1_4                             0x000062e0   Number         4  empty.o(.text.xunji)
    __arm_cp.1_5                             0x000062e4   Number         4  empty.o(.text.xunji)
    __arm_cp.1_6                             0x000062e8   Number         4  empty.o(.text.xunji)
    __arm_cp.1_7                             0x000062ec   Number         4  empty.o(.text.xunji)
    __arm_cp.1_8                             0x000062f0   Number         4  empty.o(.text.xunji)
    __arm_cp.1_9                             0x000062f4   Number         4  empty.o(.text.xunji)
    __arm_cp.1_10                            0x000062f8   Number         4  empty.o(.text.xunji)
    __arm_cp.1_11                            0x000062fc   Number         4  empty.o(.text.xunji)
    __arm_cp.1_12                            0x00006300   Number         4  empty.o(.text.xunji)
    __arm_cp.1_13                            0x00006304   Number         4  empty.o(.text.xunji)
    __arm_cp.1_14                            0x00006308   Number         4  empty.o(.text.xunji)
    __arm_cp.1_15                            0x0000630c   Number         4  empty.o(.text.xunji)
    __arm_cp.1_16                            0x00006310   Number         4  empty.o(.text.xunji)
    i.__0printf                              0x00006314   Section        0  printfa.o(i.__0printf)
    i.__ARM_clz                              0x00006334   Section        0  depilogue.o(i.__ARM_clz)
    i.__ARM_fpclassify                       0x00006364   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__kernel_poly                          0x00006390   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_infnan                   0x0000643c   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_infnan2                  0x00006446   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x0000644e   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_underflow                0x00006460   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__scatterload_copy                     0x00006478   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x00006488   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x00006490   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x000064a0   Section        0  errno.o(i.__set_errno)
    _fp_digits                               0x000064ad   Thumb Code   344  printfa.o(i._fp_digits)
    i._fp_digits                             0x000064ac   Section        0  printfa.o(i._fp_digits)
    _printf_core                             0x00006621   Thumb Code  1754  printfa.o(i._printf_core)
    i._printf_core                           0x00006620   Section        0  printfa.o(i._printf_core)
    _printf_post_padding                     0x00006d0d   Thumb Code    32  printfa.o(i._printf_post_padding)
    i._printf_post_padding                   0x00006d0c   Section        0  printfa.o(i._printf_post_padding)
    _printf_pre_padding                      0x00006d2d   Thumb Code    44  printfa.o(i._printf_pre_padding)
    i._printf_pre_padding                    0x00006d2c   Section        0  printfa.o(i._printf_pre_padding)
    i.asin                                   0x00006d58   Section        0  asin.o(i.asin)
    i.atan                                   0x00006fcc   Section        0  atan.o(i.atan)
    i.atan2                                  0x000071e8   Section        0  atan2.o(i.atan2)
    i.roundf                                 0x00007384   Section        0  roundf.o(i.roundf)
    i.sqrt                                   0x000073f8   Section        0  sqrt.o(i.sqrt)
    pS                                       0x00007440   Data          48  asin.o(.constdata)
    .constdata                               0x00007440   Section       80  asin.o(.constdata)
    qS                                       0x00007470   Data          32  asin.o(.constdata)
    atanhi                                   0x00007490   Data          32  atan.o(.constdata)
    .constdata                               0x00007490   Section      152  atan.o(.constdata)
    atanlo                                   0x000074b0   Data          32  atan.o(.constdata)
    aTodd                                    0x000074d0   Data          40  atan.o(.constdata)
    aTeven                                   0x000074f8   Data          48  atan.o(.constdata)
    .constdata                               0x00007528   Section        8  qnan.o(.constdata)
    [Anonymous Symbol]                       0x00007530   Section        0  inv_mpu_dmp_motion_driver.o(.rodata..L__const.dmp_set_orientation.accel_axes)
    [Anonymous Symbol]                       0x00007533   Section        0  inv_mpu_dmp_motion_driver.o(.rodata..L__const.dmp_set_orientation.gyro_axes)
    [Anonymous Symbol]                       0x00007b26   Section        0  inv_mpu.o(.rodata.cst8)
    dmp_memory                               0x00007b2e   Data        3062  inv_mpu_dmp_motion_driver.o(.rodata.dmp_memory)
    [Anonymous Symbol]                       0x00007b2e   Section        0  inv_mpu_dmp_motion_driver.o(.rodata.dmp_memory)
    gADC12_0ClockConfig                      0x00008724   Data           8  ti_msp_dl_config.o(.rodata.gADC12_0ClockConfig)
    [Anonymous Symbol]                       0x00008724   Section        0  ti_msp_dl_config.o(.rodata.gADC12_0ClockConfig)
    gDebugTimerClockConfig                   0x0000872c   Data           3  ti_msp_dl_config.o(.rodata.gDebugTimerClockConfig)
    [Anonymous Symbol]                       0x0000872c   Section        0  ti_msp_dl_config.o(.rodata.gDebugTimerClockConfig)
    gDebugTimerTimerConfig                   0x00008730   Data          20  ti_msp_dl_config.o(.rodata.gDebugTimerTimerConfig)
    [Anonymous Symbol]                       0x00008730   Section        0  ti_msp_dl_config.o(.rodata.gDebugTimerTimerConfig)
    gI2C_0ClockConfig                        0x00008744   Data           2  ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig)
    [Anonymous Symbol]                       0x00008744   Section        0  ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig)
    gPWM_0ClockConfig                        0x00008746   Data           3  ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig)
    [Anonymous Symbol]                       0x00008746   Section        0  ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig)
    gPWM_0Config                             0x0000874c   Data           8  ti_msp_dl_config.o(.rodata.gPWM_0Config)
    [Anonymous Symbol]                       0x0000874c   Section        0  ti_msp_dl_config.o(.rodata.gPWM_0Config)
    gSYSPLLConfig                            0x00008754   Data          40  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    [Anonymous Symbol]                       0x00008754   Section        0  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    gTIMER_0ClockConfig                      0x0000877c   Data           3  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    [Anonymous Symbol]                       0x0000877c   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    gTIMER_0TimerConfig                      0x00008780   Data          20  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    [Anonymous Symbol]                       0x00008780   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    gUART_0ClockConfig                       0x00008794   Data           2  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    [Anonymous Symbol]                       0x00008794   Section        0  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    gUART_0Config                            0x00008796   Data          10  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x00008796   Section        0  ti_msp_dl_config.o(.rodata.gUART_0Config)
    gUART_2ClockConfig                       0x000087a0   Data           2  ti_msp_dl_config.o(.rodata.gUART_2ClockConfig)
    [Anonymous Symbol]                       0x000087a0   Section        0  ti_msp_dl_config.o(.rodata.gUART_2ClockConfig)
    gUART_2Config                            0x000087a2   Data          10  ti_msp_dl_config.o(.rodata.gUART_2Config)
    [Anonymous Symbol]                       0x000087a2   Section        0  ti_msp_dl_config.o(.rodata.gUART_2Config)
    [Anonymous Symbol]                       0x000087d3   Section        0  empty.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x000087df   Section        0  inv_mpu.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0000882c   Section        0  mpu6050.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x000088db   Section        0  show.o(.rodata.str1.1)
    .data                                    0x20200000   Section        4  stdout.o(.data)
    _errno                                   0x20200004   Data           4  errno.o(.data)
    .data                                    0x20200004   Section        4  errno.o(.data)
    st                                       0x20200068   Data          44  inv_mpu.o(.data.st)
    [Anonymous Symbol]                       0x20200068   Section        0  inv_mpu.o(.data.st)
    BalanceControlTask.Velocity_Pwm_L        0x20200098   Data           4  balance.o(.bss.BalanceControlTask.Velocity_Pwm_L)
    [Anonymous Symbol]                       0x20200098   Section        0  balance.o(.bss.BalanceControlTask.Velocity_Pwm_L)
    BalanceControlTask.Velocity_Pwm_R        0x2020009c   Data           4  balance.o(.bss.BalanceControlTask.Velocity_Pwm_R)
    [Anonymous Symbol]                       0x2020009c   Section        0  balance.o(.bss.BalanceControlTask.Velocity_Pwm_R)
    Get_Vol.adcVal                           0x202000a8   Data           2  balance.o(.bss.Get_Vol.adcVal)
    [Anonymous Symbol]                       0x202000a8   Section        0  balance.o(.bss.Get_Vol.adcVal)
    Get_Vol.startflag                        0x202000aa   Data           1  balance.o(.bss.Get_Vol.startflag)
    [Anonymous Symbol]                       0x202000aa   Section        0  balance.o(.bss.Get_Vol.startflag)
    dmp.0                                    0x20200108   Data           4  inv_mpu_dmp_motion_driver.o(.bss.dmp.0)
    [Anonymous Symbol]                       0x20200108   Section        0  inv_mpu_dmp_motion_driver.o(.bss.dmp.0)
    dmp.1                                    0x2020010c   Data           4  inv_mpu_dmp_motion_driver.o(.bss.dmp.1)
    [Anonymous Symbol]                       0x2020010c   Section        0  inv_mpu_dmp_motion_driver.o(.bss.dmp.1)
    dmp.2                                    0x20200110   Data           2  inv_mpu_dmp_motion_driver.o(.bss.dmp.2)
    [Anonymous Symbol]                       0x20200110   Section        0  inv_mpu_dmp_motion_driver.o(.bss.dmp.2)
    dmp.3                                    0x20200114   Data           2  inv_mpu_dmp_motion_driver.o(.bss.dmp.3)
    [Anonymous Symbol]                       0x20200114   Section        0  inv_mpu_dmp_motion_driver.o(.bss.dmp.3)
    dmp.4                                    0x20200118   Data           2  inv_mpu_dmp_motion_driver.o(.bss.dmp.4)
    [Anonymous Symbol]                       0x20200118   Section        0  inv_mpu_dmp_motion_driver.o(.bss.dmp.4)
    dmp.5                                    0x2020011a   Data           1  inv_mpu_dmp_motion_driver.o(.bss.dmp.5)
    [Anonymous Symbol]                       0x2020011a   Section        0  inv_mpu_dmp_motion_driver.o(.bss.dmp.5)
    key_scan.check_once                      0x20200270   Data           1  bsp_key.o(.bss.key_scan.check_once)
    [Anonymous Symbol]                       0x20200270   Section        0  bsp_key.o(.bss.key_scan.check_once)
    key_scan.long_press_time                 0x20200272   Data           2  bsp_key.o(.bss.key_scan.long_press_time)
    [Anonymous Symbol]                       0x20200272   Section        0  bsp_key.o(.bss.key_scan.long_press_time)
    key_scan.press_flag                      0x20200274   Data           1  bsp_key.o(.bss.key_scan.press_flag)
    [Anonymous Symbol]                       0x20200274   Section        0  bsp_key.o(.bss.key_scan.press_flag)
    key_scan.time_core                       0x20200276   Data           2  bsp_key.o(.bss.key_scan.time_core)
    [Anonymous Symbol]                       0x20200276   Section        0  bsp_key.o(.bss.key_scan.time_core)
    main.hd6_debounce                        0x2020027c   Data           4  empty.o(.bss.main.hd6_debounce)
    [Anonymous Symbol]                       0x2020027c   Section        0  empty.o(.bss.main.hd6_debounce)
    main.key0_debounce                       0x20200280   Data           4  empty.o(.bss.main.key0_debounce)
    [Anonymous Symbol]                       0x20200280   Section        0  empty.o(.bss.main.key0_debounce)
    oled_show.display_counter                0x202002d0   Data           4  show.o(.bss.oled_show.display_counter)
    [Anonymous Symbol]                       0x202002d0   Section        0  show.o(.bss.oled_show.display_counter)
    STACK                                    0x20200328   Section     4096  startup_mspm0g350x_uvision.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g350x_uvision.o(RESET)
    _printf_a                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_End                            0x000000c0   Data           0  startup_mspm0g350x_uvision.o(RESET)
    __Vectors_Size                           0x000000c0   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    __main                                   0x000000c1   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x000000c1   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x000000c5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x000000c9   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x000000c9   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x000000c9   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x000000c9   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x000000d1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x000000d1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x000000d5   Thumb Code     4  startup_mspm0g350x_uvision.o(.text)
    NMI_Handler                              0x000000d9   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    HardFault_Handler                        0x000000db   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SVC_Handler                              0x000000dd   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    PendSV_Handler                           0x000000df   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SysTick_Handler                          0x000000e1   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    ADC0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    AES_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    CANFD0_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DAC0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DMA_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    Default_Handler                          0x000000e3   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    GROUP0_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    RTC_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA0_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA1_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG0_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG12_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG6_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG7_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG8_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART1_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART3_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    __aeabi_ldivmod                          0x000000e9   Thumb Code    76  ldiv.o(.text)
    __aeabi_lmul                             0x00000135   Thumb Code    48  llmul.o(.text)
    _ll_mul                                  0x00000135   Thumb Code     0  llmul.o(.text)
    __aeabi_memset                           0x00000165   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x00000165   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x00000165   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x00000173   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x00000173   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x00000173   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x00000177   Thumb Code    18  memseta.o(.text)
    strlen                                   0x00000189   Thumb Code    14  strlen.o(.text)
    memcmp                                   0x00000197   Thumb Code    26  memcmp.o(.text)
    __aeabi_fadd                             0x000001b1   Thumb Code   162  fadd.o(.text)
    __aeabi_fsub                             0x00000253   Thumb Code     8  fadd.o(.text)
    __aeabi_frsub                            0x0000025b   Thumb Code     8  fadd.o(.text)
    __aeabi_fmul                             0x00000263   Thumb Code   122  fmul.o(.text)
    __aeabi_fdiv                             0x000002dd   Thumb Code   124  fdiv.o(.text)
    __aeabi_dadd                             0x00000359   Thumb Code   328  dadd.o(.text)
    __aeabi_dsub                             0x000004a1   Thumb Code    12  dadd.o(.text)
    __aeabi_drsub                            0x000004ad   Thumb Code    12  dadd.o(.text)
    __aeabi_dmul                             0x000004bd   Thumb Code   202  dmul.o(.text)
    __aeabi_ddiv                             0x0000058d   Thumb Code   234  ddiv.o(.text)
    __aeabi_fcmple                           0x0000067d   Thumb Code    28  fcmple.o(.text)
    __aeabi_fcmplt                           0x00000699   Thumb Code    28  fcmplt.o(.text)
    __aeabi_fcmpge                           0x000006b5   Thumb Code    28  fcmpge.o(.text)
    __aeabi_fcmpgt                           0x000006d1   Thumb Code    28  fcmpgt.o(.text)
    __aeabi_fcmpeq                           0x000006ed   Thumb Code    28  fcmpeq.o(.text)
    __aeabi_i2f                              0x00000709   Thumb Code    22  fflti.o(.text)
    __aeabi_ui2f                             0x0000071f   Thumb Code    14  ffltui.o(.text)
    __aeabi_i2d                              0x0000072d   Thumb Code    34  dflti.o(.text)
    __aeabi_f2iz                             0x00000755   Thumb Code    50  ffixi.o(.text)
    __aeabi_f2uiz                            0x00000787   Thumb Code    40  ffixui.o(.text)
    __aeabi_f2d                              0x000007af   Thumb Code    40  f2d.o(.text)
    __aeabi_d2f                              0x000007d7   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x0000080f   Thumb Code     0  uidiv_div0.o(.text)
    __aeabi_uidivmod                         0x0000080f   Thumb Code    62  uidiv_div0.o(.text)
    __aeabi_idiv                             0x0000084d   Thumb Code     0  idiv_div0.o(.text)
    __aeabi_idivmod                          0x0000084d   Thumb Code    74  idiv_div0.o(.text)
    __aeabi_uldivmod                         0x0000089d   Thumb Code    96  uldiv.o(.text)
    __aeabi_llsl                             0x000008fd   Thumb Code    32  llshl.o(.text)
    _ll_shift_l                              0x000008fd   Thumb Code     0  llshl.o(.text)
    __aeabi_lasr                             0x0000091d   Thumb Code    38  llsshr.o(.text)
    _ll_sshift_r                             0x0000091d   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x00000943   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x00000943   Thumb Code    16  fepilogue.o(.text)
    _float_epilogue                          0x00000953   Thumb Code   114  fepilogue.o(.text)
    _frnd                                    0x000009c5   Thumb Code    62  frnd.o(.text)
    _double_round                            0x00000a03   Thumb Code    26  depilogue.o(.text)
    _double_epilogue                         0x00000a1d   Thumb Code   164  depilogue.o(.text)
    __ARM_scalbn                             0x00000ac1   Thumb Code    44  dscalb.o(.text)
    scalbn                                   0x00000ac1   Thumb Code     0  dscalb.o(.text)
    __aeabi_d2ulz                            0x00000aed   Thumb Code    54  dfixul.o(.text)
    __aeabi_cdcmpeq                          0x00000b2d   Thumb Code     0  cdcmple.o(.text)
    __aeabi_cdcmple                          0x00000b2d   Thumb Code    38  cdcmple.o(.text)
    __aeabi_cdrcmple                         0x00000b55   Thumb Code    38  cdrcmple.o(.text)
    __scatterload                            0x00000b81   Thumb Code    38  init.o(.text)
    __scatterload_rt2                        0x00000b81   Thumb Code     0  init.o(.text)
    __aeabi_llsr                             0x00000bb1   Thumb Code    34  llushr.o(.text)
    _ll_ushift_r                             0x00000bb1   Thumb Code     0  llushr.o(.text)
    _dsqrt                                   0x00000bd3   Thumb Code   162  dsqrt.o(.text)
    ADC1_IRQHandler                          0x00000c75   Thumb Code    20  balance.o(.text.ADC1_IRQHandler)
    BalanceControlTask                       0x00000c8d   Thumb Code   640  balance.o(.text.BalanceControlTask)
    DL_ADC12_setClockConfig                  0x00000f9d   Thumb Code    56  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    DL_Common_delayCycles                    0x00000fdd   Thumb Code    10  dl_common.o(.text.DL_Common_delayCycles)
    DL_I2C_fillControllerTXFIFO              0x00000fe7   Thumb Code   128  dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO)
    DL_I2C_flushControllerTXFIFO             0x00001069   Thumb Code    64  dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO)
    DL_I2C_setClockConfig                    0x000010ad   Thumb Code    38  dl_i2c.o(.text.DL_I2C_setClockConfig)
    DL_SYSCTL_configSYSPLL                   0x000010d5   Thumb Code   196  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK    0x00001199   Thumb Code    40  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    DL_TimerA_initPWMMode                    0x000011c1   Thumb Code   120  dl_timer.o(.text.DL_TimerA_initPWMMode)
    DL_Timer_initPWMMode                     0x00001245   Thumb Code   188  dl_timer.o(.text.DL_Timer_initPWMMode)
    DL_Timer_initTimerMode                   0x00001319   Thumb Code   224  dl_timer.o(.text.DL_Timer_initTimerMode)
    DL_Timer_setCaptCompUpdateMethod         0x00001405   Thumb Code    24  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    DL_Timer_setCaptureCompareOutCtl         0x00001421   Thumb Code    20  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    DL_Timer_setCaptureCompareValue          0x00001439   Thumb Code    12  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    DL_Timer_setClockConfig                  0x00001449   Thumb Code    24  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_UART_init                             0x00001465   Thumb Code    64  dl_uart.o(.text.DL_UART_init)
    DL_UART_setClockConfig                   0x000014ad   Thumb Code    18  dl_uart.o(.text.DL_UART_setClockConfig)
    DL_UART_transmitDataBlocking             0x000014c1   Thumb Code    32  dl_uart.o(.text.DL_UART_transmitDataBlocking)
    DMP_Init                                 0x000014e5   Thumb Code   364  mpu6050.o(.text.DMP_Init)
    GROUP1_IRQHandler                        0x000016f1   Thumb Code   284  empty.o(.text.GROUP1_IRQHandler)
    K210_Receive_Data                        0x00001999   Thumb Code   304  empty.o(.text.K210_Receive_Data)
    MPU6050_initialize                       0x00001ad9   Thumb Code   328  mpu6050.o(.text.MPU6050_initialize)
    OLED_Clear                               0x00001c31   Thumb Code   132  bsp_oled.o(.text.OLED_Clear)
    OLED_I2C_SendByte                        0x00001cb5   Thumb Code   320  bsp_oled.o(.text.OLED_I2C_SendByte)
    OLED_Init                                0x00001df9   Thumb Code  1980  bsp_oled.o(.text.OLED_Init)
    OLED_SetCursor                           0x000025b9   Thumb Code   244  bsp_oled.o(.text.OLED_SetCursor)
    OLED_ShowChar                            0x000026ad   Thumb Code   272  bsp_oled.o(.text.OLED_ShowChar)
    OLED_ShowString                          0x00002a29   Thumb Code    44  bsp_oled.o(.text.OLED_ShowString)
    Read_DMP                                 0x00002a91   Thumb Code   460  mpu6050.o(.text.Read_DMP)
    SYSCFG_DL_ADC12_0_init                   0x00002c8d   Thumb Code    60  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init)
    SYSCFG_DL_DebugTimer_init                0x00002cdd   Thumb Code    28  ti_msp_dl_config.o(.text.SYSCFG_DL_DebugTimer_init)
    SYSCFG_DL_GPIO_init                      0x00002d09   Thumb Code   208  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_I2C_0_init                     0x00002e11   Thumb Code    72  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    SYSCFG_DL_PWM_0_init                     0x00002e65   Thumb Code   116  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    SYSCFG_DL_SYSCTL_init                    0x00002ef1   Thumb Code   116  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_SYSTICK_init                   0x00002f75   Thumb Code    24  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    SYSCFG_DL_TIMER_0_init                   0x00002f95   Thumb Code    36  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    SYSCFG_DL_UART_0_init                    0x00002fcd   Thumb Code    92  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    SYSCFG_DL_UART_2_init                    0x0000303d   Thumb Code   104  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    SYSCFG_DL_init                           0x000030c1   Thumb Code    64  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x00003109   Thumb Code    80  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    Systick_getTick                          0x00003185   Thumb Code     8  bsp_systick.o(.text.Systick_getTick)
    UART0_IRQHandler                         0x0000318d   Thumb Code    20  empty.o(.text.UART0_IRQHandler)
    UART2_IRQHandler                         0x000031a9   Thumb Code    32  empty.o(.text.UART2_IRQHandler)
    delay_ms                                 0x000031d5   Thumb Code   116  bsp_systick.o(.text.delay_ms)
    delay_us                                 0x0000324d   Thumb Code   120  bsp_systick.o(.text.delay_us)
    dmp_enable_feature                       0x000032d1   Thumb Code   644  inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature)
    dmp_load_motion_driver_firmware          0x00003575   Thumb Code    20  inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware)
    dmp_read_fifo                            0x00003591   Thumb Code   404  inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo)
    dmp_set_accel_bias                       0x00003735   Thumb Code   196  inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias)
    dmp_set_fifo_rate                        0x000037f9   Thumb Code    84  inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate)
    dmp_set_gyro_bias                        0x00003865   Thumb Code   228  inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias)
    dmp_set_orientation                      0x0000394d   Thumb Code   192  inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation)
    dmp_set_tap_thresh                       0x00003a25   Thumb Code   288  inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh)
    fputc                                    0x00003b59   Thumb Code    44  bsp_printf.o(.text.fputc)
    main                                     0x000040c9   Thumb Code   648  empty.o(.text.main)
    mpu6050_i2c_sda_unlock                   0x00004451   Thumb Code   112  bsp_iic.o(.text.mpu6050_i2c_sda_unlock)
    mpu_configure_fifo                       0x000044dd   Thumb Code   188  inv_mpu.o(.text.mpu_configure_fifo)
    mpu_get_accel_fsr                        0x00004599   Thumb Code    40  inv_mpu.o(.text.mpu_get_accel_fsr)
    mpu_get_accel_sens                       0x000045c9   Thumb Code    40  inv_mpu.o(.text.mpu_get_accel_sens)
    mpu_get_gyro_sens                        0x00004605   Thumb Code    52  inv_mpu.o(.text.mpu_get_gyro_sens)
    mpu_init                                 0x00004649   Thumb Code   416  inv_mpu.o(.text.mpu_init)
    mpu_load_firmware                        0x00004841   Thumb Code   364  inv_mpu.o(.text.mpu_load_firmware)
    mpu_lp_accel_mode                        0x000049b1   Thumb Code   600  inv_mpu.o(.text.mpu_lp_accel_mode)
    mpu_read_fifo_stream                     0x00004c11   Thumb Code   208  inv_mpu.o(.text.mpu_read_fifo_stream)
    mpu_reset_fifo                           0x00004ce9   Thumb Code   412  inv_mpu.o(.text.mpu_reset_fifo)
    mpu_run_self_test                        0x00004e8d   Thumb Code  2088  inv_mpu.o(.text.mpu_run_self_test)
    mpu_set_accel_fsr                        0x000056e5   Thumb Code   144  inv_mpu.o(.text.mpu_set_accel_fsr)
    mpu_set_bypass                           0x00005775   Thumb Code   292  inv_mpu.o(.text.mpu_set_bypass)
    mpu_set_dmp_state                        0x0000589d   Thumb Code   252  inv_mpu.o(.text.mpu_set_dmp_state)
    mpu_set_lpf                              0x0000599d   Thumb Code   132  inv_mpu.o(.text.mpu_set_lpf)
    mpu_set_sample_rate                      0x00005a29   Thumb Code   240  inv_mpu.o(.text.mpu_set_sample_rate)
    mpu_set_sensors                          0x00005b21   Thumb Code   244  inv_mpu.o(.text.mpu_set_sensors)
    mpu_write_mem                            0x00005c19   Thumb Code   124  inv_mpu.o(.text.mpu_write_mem)
    mspm0_i2c_read                           0x00005c9d   Thumb Code   228  bsp_iic.o(.text.mspm0_i2c_read)
    myget_ms                                 0x00005d95   Thumb Code     2  inv_mpu.o(.text.myget_ms)
    oled_show                                0x00005d99   Thumb Code   780  show.o(.text.oled_show)
    puts                                     0x0000613d   Thumb Code   100  bsp_printf.o(.text.puts)
    xunji                                    0x000061a9   Thumb Code   296  empty.o(.text.xunji)
    __0printf                                0x00006315   Thumb Code    24  printfa.o(i.__0printf)
    __1printf                                0x00006315   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x00006315   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x00006315   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x00006315   Thumb Code     0  printfa.o(i.__0printf)
    __ARM_clz                                0x00006335   Thumb Code    46  depilogue.o(i.__ARM_clz)
    __ARM_fpclassify                         0x00006365   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    __kernel_poly                            0x00006391   Thumb Code   172  poly.o(i.__kernel_poly)
    __mathlib_dbl_infnan                     0x0000643d   Thumb Code    10  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_infnan2                    0x00006447   Thumb Code     8  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x0000644f   Thumb Code    16  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_underflow                  0x00006461   Thumb Code    14  dunder.o(i.__mathlib_dbl_underflow)
    __scatterload_copy                       0x00006479   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x00006489   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x00006491   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x000064a1   Thumb Code     6  errno.o(i.__set_errno)
    asin                                     0x00006d59   Thumb Code   574  asin.o(i.asin)
    atan                                     0x00006fcd   Thumb Code   472  atan.o(i.atan)
    atan2                                    0x000071e9   Thumb Code   372  atan2.o(i.atan2)
    roundf                                   0x00007385   Thumb Code   116  roundf.o(i.roundf)
    sqrt                                     0x000073f9   Thumb Code    66  sqrt.o(i.sqrt)
    __mathlib_zero                           0x00007528   Data           8  qnan.o(.constdata)
    OLED_F8x16                               0x00007536   Data        1520  bsp_oled.o(.rodata.OLED_F8x16)
    hw                                       0x000087ac   Data          12  inv_mpu.o(.rodata.hw)
    reg                                      0x000087b8   Data          27  inv_mpu.o(.rodata.reg)
    test                                     0x000088e8   Data          40  inv_mpu.o(.rodata.test)
    Region$$Table$$Base                      0x00008910   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x00008930   Number         0  anon$$obj.o(Region$$Table)
    __stdout                                 0x20200000   Data           4  stdout.o(.data)
    Flag_Stop                                0x20200008   Data           1  balance.o(.data.Flag_Stop)
    KEY0                                     0x2020000c   Data           4  empty.o(.data.KEY0)
    RTOSTaskDebug                            0x20200010   Data          12  bsp_debugtimer.o(.data.RTOSTaskDebug)
    Turn_Kp                                  0x2020001c   Data           4  balance.o(.data.Turn_Kp)
    UserKey                                  0x20200020   Data           4  bsp_key.o(.data.UserKey)
    UserOLED                                 0x20200024   Data          28  bsp_oled.o(.data.UserOLED)
    User_sIICDev                             0x20200040   Data          24  bsp_iic.o(.data.User_sIICDev)
    Velocity_Kp                              0x20200058   Data           4  balance.o(.data.Velocity_Kp)
    lfx                                      0x2020005c   Data           4  show.o(.data.lfx)
    mainTaskFreqCheck                        0x20200060   Data           4  empty.o(.data.mainTaskFreqCheck)
    q0                                       0x20200064   Data           4  mpu6050.o(.data.q0)
    xuanti                                   0x20200094   Data           4  empty.o(.data.xuanti)
    Encoder_Left                             0x202000a0   Data           4  balance.o(.bss.Encoder_Left)
    Encoder_Right                            0x202000a4   Data           4  balance.o(.bss.Encoder_Right)
    HD0                                      0x202000ac   Data           4  empty.o(.bss.HD0)
    HD1                                      0x202000b0   Data           4  empty.o(.bss.HD1)
    HD2                                      0x202000b4   Data           4  empty.o(.bss.HD2)
    HD3                                      0x202000b8   Data           4  empty.o(.bss.HD3)
    HD4                                      0x202000bc   Data           4  empty.o(.bss.HD4)
    HD5                                      0x202000c0   Data           4  empty.o(.bss.HD5)
    HD6                                      0x202000c4   Data           4  empty.o(.bss.HD6)
    Motor_Left                               0x202000c8   Data           4  balance.o(.bss.Motor_Left)
    Motor_Right                              0x202000cc   Data           4  balance.o(.bss.Motor_Right)
    Pitch                                    0x202000d0   Data           4  mpu6050.o(.bss.Pitch)
    Roll                                     0x202000d4   Data           4  mpu6050.o(.bss.Roll)
    Turn1                                    0x202000d8   Data           4  balance.o(.bss.Turn1)
    Velocity1                                0x202000dc   Data           4  balance.o(.bss.Velocity1)
    Velocity_Left                            0x202000e0   Data           4  balance.o(.bss.Velocity_Left)
    Velocity_Right                           0x202000e4   Data           4  balance.o(.bss.Velocity_Right)
    Yaw                                      0x202000e8   Data           4  mpu6050.o(.bss.Yaw)
    accel                                    0x202000ec   Data           6  mpu6050.o(.bss.accel)
    buffer                                   0x202000f2   Data          14  mpu6050.o(.bss.buffer)
    controlFlag                              0x20200100   Data           4  balance.o(.bss.controlFlag)
    data1                                    0x20200104   Data           2  empty.o(.bss.data1)
    flag                                     0x2020011c   Data           4  balance.o(.bss.flag)
    fx                                       0x20200120   Data           4  empty.o(.bss.fx)
    gDebugTimerBackup                        0x20200124   Data         120  ti_msp_dl_config.o(.bss.gDebugTimerBackup)
    gPWM_0Backup                             0x2020019c   Data         188  ti_msp_dl_config.o(.bss.gPWM_0Backup)
    g_EncoderACount                          0x20200258   Data           2  empty.o(.bss.g_EncoderACount)
    g_EncoderBCount                          0x2020025a   Data           2  empty.o(.bss.g_EncoderBCount)
    getVolFlag                               0x2020025c   Data           1  balance.o(.bss.getVolFlag)
    gy                                       0x20200260   Data           4  empty.o(.bss.gy)
    gyro                                     0x20200264   Data           6  mpu6050.o(.bss.gyro)
    gyst                                     0x2020026c   Data           4  empty.o(.bss.gyst)
    m6050init                                0x20200278   Data           4  empty.o(.bss.m6050init)
    mainFreqPriv                             0x20200284   Data          16  empty.o(.bss.mainFreqPriv)
    mainTaskFreq                             0x20200294   Data           1  empty.o(.bss.mainTaskFreq)
    mainTaskUseTime                          0x20200298   Data           4  empty.o(.bss.mainTaskUseTime)
    mainUseTimePriv                          0x2020029c   Data          16  empty.o(.bss.mainUseTimePriv)
    mpu6050                                  0x202002ac   Data          36  mpu6050.o(.bss.mpu6050)
    openmv                                   0x202002d4   Data          40  empty.o(.bss.openmv)
    q1                                       0x202002fc   Data           4  mpu6050.o(.bss.q1)
    q2                                       0x20200300   Data           4  mpu6050.o(.bss.q2)
    q3                                       0x20200304   Data           4  mpu6050.o(.bss.q3)
    robotVol                                 0x20200308   Data           4  balance.o(.bss.robotVol)
    sensors                                  0x2020030c   Data           2  mpu6050.o(.bss.sensors)
    state                                    0x20200310   Data           4  empty.o(.bss.state)
    state6050                                0x20200314   Data           4  empty.o(.bss.state6050)
    time                                     0x20200318   Data           4  empty.o(.bss.time)
    x                                        0x2020031c   Data           4  empty.o(.bss.x)
    y                                        0x20200320   Data           4  empty.o(.bss.y)
    __initial_sp                             0x20201328   Data           0  startup_mspm0g350x_uvision.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000000c1

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x000089c8, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x00008930, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x000000c0   Data   RO           57    RESET               startup_mspm0g350x_uvision.o
    0x000000c0   0x000000c0   0x00000000   Code   RO         1374  * .ARM.Collect$$$$00000000  mc_p.l(entry.o)
    0x000000c0   0x000000c0   0x00000004   Code   RO         1724    .ARM.Collect$$$$00000001  mc_p.l(entry2.o)
    0x000000c4   0x000000c4   0x00000004   Code   RO         1727    .ARM.Collect$$$$00000004  mc_p.l(entry5.o)
    0x000000c8   0x000000c8   0x00000000   Code   RO         1729    .ARM.Collect$$$$00000008  mc_p.l(entry7b.o)
    0x000000c8   0x000000c8   0x00000000   Code   RO         1731    .ARM.Collect$$$$0000000A  mc_p.l(entry8b.o)
    0x000000c8   0x000000c8   0x00000008   Code   RO         1732    .ARM.Collect$$$$0000000B  mc_p.l(entry9a.o)
    0x000000d0   0x000000d0   0x00000000   Code   RO         1734    .ARM.Collect$$$$0000000D  mc_p.l(entry10a.o)
    0x000000d0   0x000000d0   0x00000000   Code   RO         1736    .ARM.Collect$$$$0000000F  mc_p.l(entry11a.o)
    0x000000d0   0x000000d0   0x00000004   Code   RO         1725    .ARM.Collect$$$$00002712  mc_p.l(entry2.o)
    0x000000d4   0x000000d4   0x00000014   Code   RO           58    .text               startup_mspm0g350x_uvision.o
    0x000000e8   0x000000e8   0x0000004c   Code   RO         1385    .text               mc_p.l(ldiv.o)
    0x00000134   0x00000134   0x00000030   Code   RO         1387    .text               mc_p.l(llmul.o)
    0x00000164   0x00000164   0x00000024   Code   RO         1391    .text               mc_p.l(memseta.o)
    0x00000188   0x00000188   0x0000000e   Code   RO         1393    .text               mc_p.l(strlen.o)
    0x00000196   0x00000196   0x0000001a   Code   RO         1395    .text               mc_p.l(memcmp.o)
    0x000001b0   0x000001b0   0x000000b2   Code   RO         1660    .text               mf_p.l(fadd.o)
    0x00000262   0x00000262   0x0000007a   Code   RO         1662    .text               mf_p.l(fmul.o)
    0x000002dc   0x000002dc   0x0000007c   Code   RO         1664    .text               mf_p.l(fdiv.o)
    0x00000358   0x00000358   0x00000164   Code   RO         1666    .text               mf_p.l(dadd.o)
    0x000004bc   0x000004bc   0x000000d0   Code   RO         1668    .text               mf_p.l(dmul.o)
    0x0000058c   0x0000058c   0x000000f0   Code   RO         1670    .text               mf_p.l(ddiv.o)
    0x0000067c   0x0000067c   0x0000001c   Code   RO         1672    .text               mf_p.l(fcmple.o)
    0x00000698   0x00000698   0x0000001c   Code   RO         1674    .text               mf_p.l(fcmplt.o)
    0x000006b4   0x000006b4   0x0000001c   Code   RO         1676    .text               mf_p.l(fcmpge.o)
    0x000006d0   0x000006d0   0x0000001c   Code   RO         1678    .text               mf_p.l(fcmpgt.o)
    0x000006ec   0x000006ec   0x0000001c   Code   RO         1680    .text               mf_p.l(fcmpeq.o)
    0x00000708   0x00000708   0x00000016   Code   RO         1682    .text               mf_p.l(fflti.o)
    0x0000071e   0x0000071e   0x0000000e   Code   RO         1684    .text               mf_p.l(ffltui.o)
    0x0000072c   0x0000072c   0x00000028   Code   RO         1686    .text               mf_p.l(dflti.o)
    0x00000754   0x00000754   0x00000032   Code   RO         1688    .text               mf_p.l(ffixi.o)
    0x00000786   0x00000786   0x00000028   Code   RO         1690    .text               mf_p.l(ffixui.o)
    0x000007ae   0x000007ae   0x00000028   Code   RO         1692    .text               mf_p.l(f2d.o)
    0x000007d6   0x000007d6   0x00000038   Code   RO         1694    .text               mf_p.l(d2f.o)
    0x0000080e   0x0000080e   0x0000003e   Code   RO         1743    .text               mc_p.l(uidiv_div0.o)
    0x0000084c   0x0000084c   0x00000050   Code   RO         1745    .text               mc_p.l(idiv_div0.o)
    0x0000089c   0x0000089c   0x00000060   Code   RO         1749    .text               mc_p.l(uldiv.o)
    0x000008fc   0x000008fc   0x00000020   Code   RO         1751    .text               mc_p.l(llshl.o)
    0x0000091c   0x0000091c   0x00000026   Code   RO         1753    .text               mc_p.l(llsshr.o)
    0x00000942   0x00000942   0x00000000   Code   RO         1762    .text               mc_p.l(iusefp.o)
    0x00000942   0x00000942   0x00000082   Code   RO         1763    .text               mf_p.l(fepilogue.o)
    0x000009c4   0x000009c4   0x0000003e   Code   RO         1765    .text               mf_p.l(frnd.o)
    0x00000a02   0x00000a02   0x000000be   Code   RO         1767    .text               mf_p.l(depilogue.o)
    0x00000ac0   0x00000ac0   0x0000002c   Code   RO         1771    .text               mf_p.l(dscalb.o)
    0x00000aec   0x00000aec   0x00000040   Code   RO         1773    .text               mf_p.l(dfixul.o)
    0x00000b2c   0x00000b2c   0x00000028   Code   RO         1775    .text               mf_p.l(cdcmple.o)
    0x00000b54   0x00000b54   0x00000028   Code   RO         1777    .text               mf_p.l(cdrcmple.o)
    0x00000b7c   0x00000b7c   0x00000004   PAD
    0x00000b80   0x00000b80   0x00000030   Code   RO         1779    .text               mc_p.l(init.o)
    0x00000bb0   0x00000bb0   0x00000022   Code   RO         1781    .text               mc_p.l(llushr.o)
    0x00000bd2   0x00000bd2   0x000000a2   Code   RO         1783    .text               mf_p.l(dsqrt.o)
    0x00000c74   0x00000c74   0x00000018   Code   RO         1251    .text.ADC1_IRQHandler  balance.o
    0x00000c8c   0x00000c8c   0x00000310   Code   RO         1247    .text.BalanceControlTask  balance.o
    0x00000f9c   0x00000f9c   0x00000040   Code   RO          816    .text.DL_ADC12_setClockConfig  dl_adc12.o
    0x00000fdc   0x00000fdc   0x0000000a   Code   RO          770    .text.DL_Common_delayCycles  dl_common.o
    0x00000fe6   0x00000fe6   0x00000080   Code   RO          535    .text.DL_I2C_fillControllerTXFIFO  dl_i2c.o
    0x00001066   0x00001066   0x00000002   PAD
    0x00001068   0x00001068   0x00000044   Code   RO          537    .text.DL_I2C_flushControllerTXFIFO  dl_i2c.o
    0x000010ac   0x000010ac   0x00000026   Code   RO          531    .text.DL_I2C_setClockConfig  dl_i2c.o
    0x000010d2   0x000010d2   0x00000002   PAD
    0x000010d4   0x000010d4   0x000000c4   Code   RO         1327    .text.DL_SYSCTL_configSYSPLL  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00001198   0x00001198   0x00000028   Code   RO         1335    .text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x000011c0   0x000011c0   0x00000084   Code   RO          244    .text.DL_TimerA_initPWMMode  dl_timer.o
    0x00001244   0x00001244   0x000000d4   Code   RO          204    .text.DL_Timer_initPWMMode  dl_timer.o
    0x00001318   0x00001318   0x000000ec   Code   RO          186    .text.DL_Timer_initTimerMode  dl_timer.o
    0x00001404   0x00001404   0x0000001c   Code   RO          218    .text.DL_Timer_setCaptCompUpdateMethod  dl_timer.o
    0x00001420   0x00001420   0x00000018   Code   RO          208    .text.DL_Timer_setCaptureCompareOutCtl  dl_timer.o
    0x00001438   0x00001438   0x00000010   Code   RO          188    .text.DL_Timer_setCaptureCompareValue  dl_timer.o
    0x00001448   0x00001448   0x0000001c   Code   RO          182    .text.DL_Timer_setClockConfig  dl_timer.o
    0x00001464   0x00001464   0x00000048   Code   RO          130    .text.DL_UART_init  dl_uart.o
    0x000014ac   0x000014ac   0x00000012   Code   RO          132    .text.DL_UART_setClockConfig  dl_uart.o
    0x000014be   0x000014be   0x00000002   PAD
    0x000014c0   0x000014c0   0x00000024   Code   RO          144    .text.DL_UART_transmitDataBlocking  dl_uart.o
    0x000014e4   0x000014e4   0x0000020c   Code   RO         1215    .text.DMP_Init      mpu6050.o
    0x000016f0   0x000016f0   0x00000160   Code   RO            6    .text.GROUP1_IRQHandler  empty.o
    0x00001850   0x00001850   0x00000004   Code   RO          868    .text.HW_IIC_Master_Receive  bsp_iic.o
    0x00001854   0x00001854   0x00000004   Code   RO          866    .text.HW_IIC_Master_Transmit  bsp_iic.o
    0x00001858   0x00001858   0x00000018   Code   RO          872    .text.HW_IIC_Mem_Read  bsp_iic.o
    0x00001870   0x00001870   0x00000088   Code   RO          870    .text.HW_IIC_Mem_Write  bsp_iic.o
    0x000018f8   0x000018f8   0x00000008   Code   RO          874    .text.HW_iic_delayms  bsp_iic.o
    0x00001900   0x00001900   0x00000098   Code   RO          864    .text.HW_iic_init   bsp_iic.o
    0x00001998   0x00001998   0x00000140   Code   RO           10    .text.K210_Receive_Data  empty.o
    0x00001ad8   0x00001ad8   0x00000158   Code   RO         1213    .text.MPU6050_initialize  mpu6050.o
    0x00001c30   0x00001c30   0x00000084   Code   RO          912    .text.OLED_Clear    bsp_oled.o
    0x00001cb4   0x00001cb4   0x00000144   Code   RO          904    .text.OLED_I2C_SendByte  bsp_oled.o
    0x00001df8   0x00001df8   0x000007bc   Code   RO          924    .text.OLED_Init     bsp_oled.o
    0x000025b4   0x000025b4   0x00000002   Code   RO          934    .text.OLED_RefreshGram_Wrapper  bsp_oled.o
    0x000025b6   0x000025b6   0x00000002   PAD
    0x000025b8   0x000025b8   0x000000f4   Code   RO          910    .text.OLED_SetCursor  bsp_oled.o
    0x000026ac   0x000026ac   0x0000011c   Code   RO          914    .text.OLED_ShowChar  bsp_oled.o
    0x000027c8   0x000027c8   0x0000001e   Code   RO          926    .text.OLED_ShowChar_Wrapper  bsp_oled.o
    0x000027e6   0x000027e6   0x00000002   PAD
    0x000027e8   0x000027e8   0x00000198   Code   RO          932    .text.OLED_ShowFloat_Wrapper  bsp_oled.o
    0x00002980   0x00002980   0x000000a8   Code   RO          928    .text.OLED_ShowNumber_Wrapper  bsp_oled.o
    0x00002a28   0x00002a28   0x0000002c   Code   RO          916    .text.OLED_ShowString  bsp_oled.o
    0x00002a54   0x00002a54   0x0000003c   Code   RO          930    .text.OLED_ShowString_Wrapper  bsp_oled.o
    0x00002a90   0x00002a90   0x000001fc   Code   RO         1217    .text.Read_DMP      mpu6050.o
    0x00002c8c   0x00002c8c   0x00000050   Code   RO           85    .text.SYSCFG_DL_ADC12_0_init  ti_msp_dl_config.o
    0x00002cdc   0x00002cdc   0x0000002c   Code   RO           77    .text.SYSCFG_DL_DebugTimer_init  ti_msp_dl_config.o
    0x00002d08   0x00002d08   0x00000108   Code   RO           69    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x00002e10   0x00002e10   0x00000054   Code   RO           79    .text.SYSCFG_DL_I2C_0_init  ti_msp_dl_config.o
    0x00002e64   0x00002e64   0x0000008c   Code   RO           73    .text.SYSCFG_DL_PWM_0_init  ti_msp_dl_config.o
    0x00002ef0   0x00002ef0   0x00000084   Code   RO           71    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x00002f74   0x00002f74   0x00000020   Code   RO           87    .text.SYSCFG_DL_SYSTICK_init  ti_msp_dl_config.o
    0x00002f94   0x00002f94   0x00000038   Code   RO           75    .text.SYSCFG_DL_TIMER_0_init  ti_msp_dl_config.o
    0x00002fcc   0x00002fcc   0x00000070   Code   RO           81    .text.SYSCFG_DL_UART_0_init  ti_msp_dl_config.o
    0x0000303c   0x0000303c   0x00000084   Code   RO           83    .text.SYSCFG_DL_UART_2_init  ti_msp_dl_config.o
    0x000030c0   0x000030c0   0x00000048   Code   RO           65    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x00003108   0x00003108   0x0000007c   Code   RO           67    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x00003184   0x00003184   0x00000008   Code   RO          963    .text.Systick_getTick  bsp_systick.o
    0x0000318c   0x0000318c   0x0000001c   Code   RO            8    .text.UART0_IRQHandler  empty.o
    0x000031a8   0x000031a8   0x0000002c   Code   RO           12    .text.UART2_IRQHandler  empty.o
    0x000031d4   0x000031d4   0x00000078   Code   RO          965    .text.delay_ms      bsp_systick.o
    0x0000324c   0x0000324c   0x00000084   Code   RO          967    .text.delay_us      bsp_systick.o
    0x000032d0   0x000032d0   0x000002a4   Code   RO         1120    .text.dmp_enable_feature  inv_mpu_dmp_motion_driver.o
    0x00003574   0x00003574   0x0000001c   Code   RO         1084    .text.dmp_load_motion_driver_firmware  inv_mpu_dmp_motion_driver.o
    0x00003590   0x00003590   0x000001a4   Code   RO         1132    .text.dmp_read_fifo  inv_mpu_dmp_motion_driver.o
    0x00003734   0x00003734   0x000000c4   Code   RO         1090    .text.dmp_set_accel_bias  inv_mpu_dmp_motion_driver.o
    0x000037f8   0x000037f8   0x0000006c   Code   RO         1092    .text.dmp_set_fifo_rate  inv_mpu_dmp_motion_driver.o
    0x00003864   0x00003864   0x000000e8   Code   RO         1088    .text.dmp_set_gyro_bias  inv_mpu_dmp_motion_driver.o
    0x0000394c   0x0000394c   0x000000d8   Code   RO         1086    .text.dmp_set_orientation  inv_mpu_dmp_motion_driver.o
    0x00003a24   0x00003a24   0x00000134   Code   RO         1096    .text.dmp_set_tap_thresh  inv_mpu_dmp_motion_driver.o
    0x00003b58   0x00003b58   0x00000034   Code   RO          948    .text.fputc         bsp_printf.o
    0x00003b8c   0x00003b8c   0x0000004c   Code   RO          843    .text.get_Freq      bsp_debugtimer.o
    0x00003bd8   0x00003bd8   0x00000008   Code   RO          841    .text.get_StartCount  bsp_debugtimer.o
    0x00003be0   0x00003be0   0x0000003c   Code   RO          845    .text.get_UsedTime  bsp_debugtimer.o
    0x00003c1c   0x00003c1c   0x000003ac   Code   RO         1053    .text.get_st_biases  inv_mpu.o
    0x00003fc8   0x00003fc8   0x00000100   Code   RO          885    .text.key_scan      bsp_key.o
    0x000040c8   0x000040c8   0x00000388   Code   RO            2    .text.main          empty.o
    0x00004450   0x00004450   0x0000008c   Code   RO          858    .text.mpu6050_i2c_sda_unlock  bsp_iic.o
    0x000044dc   0x000044dc   0x000000bc   Code   RO          999    .text.mpu_configure_fifo  inv_mpu.o
    0x00004598   0x00004598   0x00000030   Code   RO         1023    .text.mpu_get_accel_fsr  inv_mpu.o
    0x000045c8   0x000045c8   0x0000003c   Code   RO         1035    .text.mpu_get_accel_sens  inv_mpu.o
    0x00004604   0x00004604   0x00000044   Code   RO         1033    .text.mpu_get_gyro_sens  inv_mpu.o
    0x00004648   0x00004648   0x000001f8   Code   RO          989    .text.mpu_init      inv_mpu.o
    0x00004840   0x00004840   0x00000170   Code   RO         1059    .text.mpu_load_firmware  inv_mpu.o
    0x000049b0   0x000049b0   0x00000260   Code   RO         1005    .text.mpu_lp_accel_mode  inv_mpu.o
    0x00004c10   0x00004c10   0x000000d8   Code   RO         1045    .text.mpu_read_fifo_stream  inv_mpu.o
    0x00004ce8   0x00004ce8   0x000001a4   Code   RO         1019    .text.mpu_reset_fifo  inv_mpu.o
    0x00004e8c   0x00004e8c   0x00000858   Code   RO         1049    .text.mpu_run_self_test  inv_mpu.o
    0x000056e4   0x000056e4   0x00000090   Code   RO          993    .text.mpu_set_accel_fsr  inv_mpu.o
    0x00005774   0x00005774   0x00000128   Code   RO         1001    .text.mpu_set_bypass  inv_mpu.o
    0x0000589c   0x0000589c   0x00000100   Code   RO         1051    .text.mpu_set_dmp_state  inv_mpu.o
    0x0000599c   0x0000599c   0x0000008c   Code   RO          995    .text.mpu_set_lpf   inv_mpu.o
    0x00005a28   0x00005a28   0x000000f8   Code   RO          997    .text.mpu_set_sample_rate  inv_mpu.o
    0x00005b20   0x00005b20   0x000000f8   Code   RO         1003    .text.mpu_set_sensors  inv_mpu.o
    0x00005c18   0x00005c18   0x00000084   Code   RO         1055    .text.mpu_write_mem  inv_mpu.o
    0x00005c9c   0x00005c9c   0x000000f8   Code   RO          862    .text.mspm0_i2c_read  bsp_iic.o
    0x00005d94   0x00005d94   0x00000002   Code   RO         1011    .text.myget_ms      inv_mpu.o
    0x00005d96   0x00005d96   0x00000002   PAD
    0x00005d98   0x00005d98   0x000003a4   Code   RO         1312    .text.oled_show     show.o
    0x0000613c   0x0000613c   0x0000006c   Code   RO          952    .text.puts          bsp_printf.o
    0x000061a8   0x000061a8   0x0000016c   Code   RO            4    .text.xunji         empty.o
    0x00006314   0x00006314   0x00000020   Code   RO         1632    i.__0printf         mc_p.l(printfa.o)
    0x00006334   0x00006334   0x0000002e   Code   RO         1769    i.__ARM_clz         mf_p.l(depilogue.o)
    0x00006362   0x00006362   0x00000002   PAD
    0x00006364   0x00006364   0x0000002c   Code   RO         1715    i.__ARM_fpclassify  m_ps.l(fpclassify.o)
    0x00006390   0x00006390   0x000000ac   Code   RO         1717    i.__kernel_poly     m_ps.l(poly.o)
    0x0000643c   0x0000643c   0x0000000a   Code   RO         1702    i.__mathlib_dbl_infnan  m_ps.l(dunder.o)
    0x00006446   0x00006446   0x00000008   Code   RO         1703    i.__mathlib_dbl_infnan2  m_ps.l(dunder.o)
    0x0000644e   0x0000644e   0x00000010   Code   RO         1704    i.__mathlib_dbl_invalid  m_ps.l(dunder.o)
    0x0000645e   0x0000645e   0x00000002   PAD
    0x00006460   0x00006460   0x00000014   Code   RO         1707    i.__mathlib_dbl_underflow  m_ps.l(dunder.o)
    0x00006474   0x00006474   0x00000004   PAD
    0x00006478   0x00006478   0x0000000e   Code   RO         1787    i.__scatterload_copy  mc_p.l(handlers.o)
    0x00006486   0x00006486   0x00000002   PAD
    0x00006488   0x00006488   0x00000002   Code   RO         1788    i.__scatterload_null  mc_p.l(handlers.o)
    0x0000648a   0x0000648a   0x00000006   PAD
    0x00006490   0x00006490   0x0000000e   Code   RO         1789    i.__scatterload_zeroinit  mc_p.l(handlers.o)
    0x0000649e   0x0000649e   0x00000002   PAD
    0x000064a0   0x000064a0   0x0000000c   Code   RO         1757    i.__set_errno       mc_p.l(errno.o)
    0x000064ac   0x000064ac   0x00000174   Code   RO         1639    i._fp_digits        mc_p.l(printfa.o)
    0x00006620   0x00006620   0x000006ec   Code   RO         1640    i._printf_core      mc_p.l(printfa.o)
    0x00006d0c   0x00006d0c   0x00000020   Code   RO         1641    i._printf_post_padding  mc_p.l(printfa.o)
    0x00006d2c   0x00006d2c   0x0000002c   Code   RO         1642    i._printf_pre_padding  mc_p.l(printfa.o)
    0x00006d58   0x00006d58   0x00000274   Code   RO         1359    i.asin              m_ps.l(asin.o)
    0x00006fcc   0x00006fcc   0x0000021c   Code   RO         1697    i.atan              m_ps.l(atan.o)
    0x000071e8   0x000071e8   0x0000019c   Code   RO         1364    i.atan2             m_ps.l(atan2.o)
    0x00007384   0x00007384   0x00000074   Code   RO         1372    i.roundf            m_ps.l(roundf.o)
    0x000073f8   0x000073f8   0x00000048   Code   RO         1721    i.sqrt              m_ps.l(sqrt.o)
    0x00007440   0x00007440   0x00000050   Data   RO         1360    .constdata          m_ps.l(asin.o)
    0x00007490   0x00007490   0x00000098   Data   RO         1698    .constdata          m_ps.l(atan.o)
    0x00007528   0x00007528   0x00000008   Data   RO         1719    .constdata          m_ps.l(qnan.o)
    0x00007530   0x00007530   0x00000003   Data   RO         1140    .rodata..L__const.dmp_set_orientation.accel_axes  inv_mpu_dmp_motion_driver.o
    0x00007533   0x00007533   0x00000003   Data   RO         1139    .rodata..L__const.dmp_set_orientation.gyro_axes  inv_mpu_dmp_motion_driver.o
    0x00007536   0x00007536   0x000005f0   Data   RO          936    .rodata.OLED_F8x16  bsp_oled.o
    0x00007b26   0x00007b26   0x00000008   Data   RO         1074    .rodata.cst8        inv_mpu.o
    0x00007b2e   0x00007b2e   0x00000bf6   Data   RO         1138    .rodata.dmp_memory  inv_mpu_dmp_motion_driver.o
    0x00008724   0x00008724   0x00000008   Data   RO          107    .rodata.gADC12_0ClockConfig  ti_msp_dl_config.o
    0x0000872c   0x0000872c   0x00000003   Data   RO          100    .rodata.gDebugTimerClockConfig  ti_msp_dl_config.o
    0x0000872f   0x0000872f   0x00000001   PAD
    0x00008730   0x00008730   0x00000014   Data   RO          101    .rodata.gDebugTimerTimerConfig  ti_msp_dl_config.o
    0x00008744   0x00008744   0x00000002   Data   RO          102    .rodata.gI2C_0ClockConfig  ti_msp_dl_config.o
    0x00008746   0x00008746   0x00000003   Data   RO           96    .rodata.gPWM_0ClockConfig  ti_msp_dl_config.o
    0x00008749   0x00008749   0x00000003   PAD
    0x0000874c   0x0000874c   0x00000008   Data   RO           97    .rodata.gPWM_0Config  ti_msp_dl_config.o
    0x00008754   0x00008754   0x00000028   Data   RO           95    .rodata.gSYSPLLConfig  ti_msp_dl_config.o
    0x0000877c   0x0000877c   0x00000003   Data   RO           98    .rodata.gTIMER_0ClockConfig  ti_msp_dl_config.o
    0x0000877f   0x0000877f   0x00000001   PAD
    0x00008780   0x00008780   0x00000014   Data   RO           99    .rodata.gTIMER_0TimerConfig  ti_msp_dl_config.o
    0x00008794   0x00008794   0x00000002   Data   RO          103    .rodata.gUART_0ClockConfig  ti_msp_dl_config.o
    0x00008796   0x00008796   0x0000000a   Data   RO          104    .rodata.gUART_0Config  ti_msp_dl_config.o
    0x000087a0   0x000087a0   0x00000002   Data   RO          105    .rodata.gUART_2ClockConfig  ti_msp_dl_config.o
    0x000087a2   0x000087a2   0x0000000a   Data   RO          106    .rodata.gUART_2Config  ti_msp_dl_config.o
    0x000087ac   0x000087ac   0x0000000c   Data   RO         1069    .rodata.hw          inv_mpu.o
    0x000087b8   0x000087b8   0x0000001b   Data   RO         1070    .rodata.reg         inv_mpu.o
    0x000087d3   0x000087d3   0x0000000c   Data   RO           39    .rodata.str1.1      empty.o
    0x000087df   0x000087df   0x0000004d   Data   RO         1073    .rodata.str1.1      inv_mpu.o
    0x0000882c   0x0000882c   0x000000af   Data   RO         1238    .rodata.str1.1      mpu6050.o
    0x000088db   0x000088db   0x0000000d   Data   RO         1318    .rodata.str1.1      show.o
    0x000088e8   0x000088e8   0x00000028   Data   RO         1071    .rodata.test        inv_mpu.o
    0x00008910   0x00008910   0x00000020   Data   RO         1786    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20200000, Load base: 0x00008930, Size: 0x00001328, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20200000   0x00008930   0x00000004   Data   RW         1738    .data               mc_p.l(stdout.o)
    0x20200004   0x00008934   0x00000004   Data   RW         1758    .data               mc_p.l(errno.o)
    0x20200008   0x00008938   0x00000001   Data   RW         1255    .data.Flag_Stop     balance.o
    0x20200009   0x00008939   0x00000003   PAD
    0x2020000c   0x0000893c   0x00000004   Data   RW           27    .data.KEY0          empty.o
    0x20200010   0x00008940   0x0000000c   Data   RW          847    .data.RTOSTaskDebug  bsp_debugtimer.o
    0x2020001c   0x0000894c   0x00000004   Data   RW         1261    .data.Turn_Kp       balance.o
    0x20200020   0x00008950   0x00000004   Data   RW          887    .data.UserKey       bsp_key.o
    0x20200024   0x00008954   0x0000001c   Data   RW          937    .data.UserOLED      bsp_oled.o
    0x20200040   0x00008970   0x00000018   Data   RW          876    .data.User_sIICDev  bsp_iic.o
    0x20200058   0x00008988   0x00000004   Data   RW         1259    .data.Velocity_Kp   balance.o
    0x2020005c   0x0000898c   0x00000004   Data   RW         1314    .data.lfx           show.o
    0x20200060   0x00008990   0x00000004   Data   RW           19    .data.mainTaskFreqCheck  empty.o
    0x20200064   0x00008994   0x00000004   Data   RW         1221    .data.q0            mpu6050.o
    0x20200068   0x00008998   0x0000002c   Data   RW         1072    .data.st            inv_mpu.o
    0x20200094   0x000089c4   0x00000004   Data   RW           22    .data.xuanti        empty.o
    0x20200098        -       0x00000004   Zero   RW         1269    .bss.BalanceControlTask.Velocity_Pwm_L  balance.o
    0x2020009c        -       0x00000004   Zero   RW         1268    .bss.BalanceControlTask.Velocity_Pwm_R  balance.o
    0x202000a0        -       0x00000004   Zero   RW         1270    .bss.Encoder_Left   balance.o
    0x202000a4        -       0x00000004   Zero   RW         1271    .bss.Encoder_Right  balance.o
    0x202000a8        -       0x00000002   Zero   RW         1280    .bss.Get_Vol.adcVal  balance.o
    0x202000aa        -       0x00000001   Zero   RW         1281    .bss.Get_Vol.startflag  balance.o
    0x202000ab   0x000089c8   0x00000001   PAD
    0x202000ac        -       0x00000004   Zero   RW           28    .bss.HD0            empty.o
    0x202000b0        -       0x00000004   Zero   RW           29    .bss.HD1            empty.o
    0x202000b4        -       0x00000004   Zero   RW           30    .bss.HD2            empty.o
    0x202000b8        -       0x00000004   Zero   RW           31    .bss.HD3            empty.o
    0x202000bc        -       0x00000004   Zero   RW           32    .bss.HD4            empty.o
    0x202000c0        -       0x00000004   Zero   RW           33    .bss.HD5            empty.o
    0x202000c4        -       0x00000004   Zero   RW           34    .bss.HD6            empty.o
    0x202000c8        -       0x00000004   Zero   RW         1272    .bss.Motor_Left     balance.o
    0x202000cc        -       0x00000004   Zero   RW         1273    .bss.Motor_Right    balance.o
    0x202000d0        -       0x00000004   Zero   RW         1236    .bss.Pitch          mpu6050.o
    0x202000d4        -       0x00000004   Zero   RW         1235    .bss.Roll           mpu6050.o
    0x202000d8        -       0x00000004   Zero   RW         1253    .bss.Turn1          balance.o
    0x202000dc        -       0x00000004   Zero   RW         1254    .bss.Velocity1      balance.o
    0x202000e0        -       0x00000004   Zero   RW         1274    .bss.Velocity_Left  balance.o
    0x202000e4        -       0x00000004   Zero   RW         1275    .bss.Velocity_Right  balance.o
    0x202000e8        -       0x00000004   Zero   RW         1237    .bss.Yaw            mpu6050.o
    0x202000ec        -       0x00000006   Zero   RW         1233    .bss.accel          mpu6050.o
    0x202000f2        -       0x0000000e   Zero   RW         1231    .bss.buffer         mpu6050.o
    0x20200100        -       0x00000004   Zero   RW         1266    .bss.controlFlag    balance.o
    0x20200104        -       0x00000002   Zero   RW           45    .bss.data1          empty.o
    0x20200106   0x000089c8   0x00000002   PAD
    0x20200108        -       0x00000004   Zero   RW         1141    .bss.dmp.0          inv_mpu_dmp_motion_driver.o
    0x2020010c        -       0x00000004   Zero   RW         1142    .bss.dmp.1          inv_mpu_dmp_motion_driver.o
    0x20200110        -       0x00000002   Zero   RW         1143    .bss.dmp.2          inv_mpu_dmp_motion_driver.o
    0x20200112   0x000089c8   0x00000002   PAD
    0x20200114        -       0x00000002   Zero   RW         1144    .bss.dmp.3          inv_mpu_dmp_motion_driver.o
    0x20200116   0x000089c8   0x00000002   PAD
    0x20200118        -       0x00000002   Zero   RW         1145    .bss.dmp.4          inv_mpu_dmp_motion_driver.o
    0x2020011a        -       0x00000001   Zero   RW         1146    .bss.dmp.5          inv_mpu_dmp_motion_driver.o
    0x2020011b   0x000089c8   0x00000001   PAD
    0x2020011c        -       0x00000004   Zero   RW         1267    .bss.flag           balance.o
    0x20200120        -       0x00000004   Zero   RW           36    .bss.fx             empty.o
    0x20200124        -       0x00000078   Zero   RW           94    .bss.gDebugTimerBackup  ti_msp_dl_config.o
    0x2020019c        -       0x000000bc   Zero   RW           93    .bss.gPWM_0Backup   ti_msp_dl_config.o
    0x20200258        -       0x00000002   Zero   RW           14    .bss.g_EncoderACount  empty.o
    0x2020025a        -       0x00000002   Zero   RW           15    .bss.g_EncoderBCount  empty.o
    0x2020025c        -       0x00000001   Zero   RW         1265    .bss.getVolFlag     balance.o
    0x2020025d   0x000089c8   0x00000003   PAD
    0x20200260        -       0x00000004   Zero   RW           18    .bss.gy             empty.o
    0x20200264        -       0x00000006   Zero   RW         1232    .bss.gyro           mpu6050.o
    0x2020026a   0x000089c8   0x00000002   PAD
    0x2020026c        -       0x00000004   Zero   RW           46    .bss.gyst           empty.o
    0x20200270        -       0x00000001   Zero   RW          891    .bss.key_scan.check_once  bsp_key.o
    0x20200271   0x000089c8   0x00000001   PAD
    0x20200272        -       0x00000002   Zero   RW          889    .bss.key_scan.long_press_time  bsp_key.o
    0x20200274        -       0x00000001   Zero   RW          890    .bss.key_scan.press_flag  bsp_key.o
    0x20200275   0x000089c8   0x00000001   PAD
    0x20200276        -       0x00000002   Zero   RW          888    .bss.key_scan.time_core  bsp_key.o
    0x20200278        -       0x00000004   Zero   RW           23    .bss.m6050init      empty.o
    0x2020027c        -       0x00000004   Zero   RW           40    .bss.main.hd6_debounce  empty.o
    0x20200280        -       0x00000004   Zero   RW           41    .bss.main.key0_debounce  empty.o
    0x20200284        -       0x00000010   Zero   RW           20    .bss.mainFreqPriv   empty.o
    0x20200294        -       0x00000001   Zero   RW           21    .bss.mainTaskFreq   empty.o
    0x20200295   0x000089c8   0x00000003   PAD
    0x20200298        -       0x00000004   Zero   RW           25    .bss.mainTaskUseTime  empty.o
    0x2020029c        -       0x00000010   Zero   RW           24    .bss.mainUseTimePriv  empty.o
    0x202002ac        -       0x00000024   Zero   RW         1225    .bss.mpu6050        mpu6050.o
    0x202002d0        -       0x00000004   Zero   RW         1319    .bss.oled_show.display_counter  show.o
    0x202002d4        -       0x00000028   Zero   RW           44    .bss.openmv         empty.o
    0x202002fc        -       0x00000004   Zero   RW         1222    .bss.q1             mpu6050.o
    0x20200300        -       0x00000004   Zero   RW         1223    .bss.q2             mpu6050.o
    0x20200304        -       0x00000004   Zero   RW         1224    .bss.q3             mpu6050.o
    0x20200308        -       0x00000004   Zero   RW         1264    .bss.robotVol       balance.o
    0x2020030c        -       0x00000002   Zero   RW         1234    .bss.sensors        mpu6050.o
    0x2020030e   0x000089c8   0x00000002   PAD
    0x20200310        -       0x00000004   Zero   RW           43    .bss.state          empty.o
    0x20200314        -       0x00000004   Zero   RW           37    .bss.state6050      empty.o
    0x20200318        -       0x00000004   Zero   RW           38    .bss.time           empty.o
    0x2020031c        -       0x00000004   Zero   RW           16    .bss.x              empty.o
    0x20200320        -       0x00000004   Zero   RW           17    .bss.y              empty.o
    0x20200324   0x000089c8   0x00000004   PAD
    0x20200328        -       0x00001000   Zero   RW           55    STACK               startup_mspm0g350x_uvision.o



  Load Region LR_BCR (Base: 0x41c00000, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BCR_CONFIG (Exec base: 0x41c00000, Load base: 0x41c00000, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****



  Load Region LR_BSL (Base: 0x41c00100, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BSL_CONFIG (Exec base: 0x41c00100, Load base: 0x41c00100, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       808        148          0          9         56      11825   balance.o
       144         16          0         12          0       4415   bsp_debugtimer.o
       716        104          0         24          0      17045   bsp_iic.o
       256         36          0          4          6       4600   bsp_key.o
      3676         28       1520         28          0      30761   bsp_oled.o
       160         16          0          0          0       5091   bsp_printf.o
       260         16          0          0          0       2423   bsp_systick.o
        64          8          0          0          0       4497   dl_adc12.o
        10          0          0          0          0        588   dl_common.o
       234          4          0          0          0       9423   dl_i2c.o
       676        204          0          0          0      31972   dl_timer.o
       126         12          0          0          0      14502   dl_uart.o
      2012        434         12         12        155      15771   empty.o
      7022        360        164         44          0      39067   inv_mpu.o
      2184        136       3068          0         15      12152   inv_mpu_dmp_motion_driver.o
      1376        224        175          4         88      21303   mpu6050.o
       932        152         13          4          4       4605   show.o
        20          4        192          0       4096        632   startup_mspm0g350x_uvision.o
      1272        272        131          0        308      42844   ti_msp_dl_config.o

    ----------------------------------------------------------------------
     21960       <USER>       <GROUP>        144       4752     273516   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        12          0          5          3         24          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       236         24          0          0          0      18490   dl_sysctl_mspm0g1x0x_g3x0x.o
       628         54         80          0          0        104   asin.o
       540         68        152          0          0        112   atan.o
       412         40          0          0          0        144   atan2.o
        54          6          0          0          0        272   dunder.o
        44          4          0          0          0         60   fpclassify.o
       172          0          0          0          0         76   poly.o
         0          0          8          0          0          0   qnan.o
       116          0          0          0          0         72   roundf.o
        72          6          0          0          0         76   sqrt.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        12          6          0          4          0         60   errno.o
        30          0          0          0          0          0   handlers.o
        80          6          0          0          0         72   idiv_div0.o
        48         10          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        76          0          0          0          0         76   ldiv.o
        48          0          0          0          0         72   llmul.o
        32          0          0          0          0         68   llshl.o
        38          0          0          0          0         68   llsshr.o
        34          0          0          0          0         68   llushr.o
        26          0          0          0          0         72   memcmp.o
        36          0          0          0          0        100   memseta.o
      2252         98          0          0          0        412   printfa.o
         0          0          0          4          0          0   stdout.o
        14          0          0          0          0         60   strlen.o
        62          0          0          0          0         72   uidiv_div0.o
        96          0          0          0          0         84   uldiv.o
        40          2          0          0          0         68   cdcmple.o
        40          2          0          0          0         68   cdrcmple.o
        56          0          0          0          0         68   d2f.o
       356          4          0          0          0        140   dadd.o
       240          6          0          0          0         84   ddiv.o
       236          0          0          0          0        216   depilogue.o
        64         10          0          0          0         68   dfixul.o
        40          6          0          0          0         68   dflti.o
       208          6          0          0          0         88   dmul.o
        44          0          0          0          0         72   dscalb.o
       162          0          0          0          0         80   dsqrt.o
        40          0          0          0          0         60   f2d.o
       178          0          0          0          0        108   fadd.o
        28          0          0          0          0         60   fcmpeq.o
        28          0          0          0          0         60   fcmpge.o
        28          0          0          0          0         60   fcmpgt.o
        28          0          0          0          0         60   fcmple.o
        28          0          0          0          0         60   fcmplt.o
       124          0          0          0          0         72   fdiv.o
       130          0          0          0          0        144   fepilogue.o
        50          0          0          0          0         60   ffixi.o
        40          0          0          0          0         60   ffixui.o
        22          0          0          0          0         68   fflti.o
        14          0          0          0          0         68   ffltui.o
       122          0          0          0          0         72   fmul.o
        62          0          0          0          0         68   frnd.o

    ----------------------------------------------------------------------
      7608        <USER>        <GROUP>          8          0      22858   Library Totals
        22          8          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       236         24          0          0          0      18490   driverlib.a
      2038        178        240          0          0        916   m_ps.l
      2904        128          0          8          0       1352   mc_p.l
      2408         36          0          0          0       2100   mf_p.l

    ----------------------------------------------------------------------
      7608        <USER>        <GROUP>          8          0      22858   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     29568       2548       5552        152       4752     293818   Grand Totals
     29568       2548       5552        152       4752     293818   ELF Image Totals
     29568       2548       5552        152          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                35120 (  34.30kB)
    Total RW  Size (RW Data + ZI Data)              4904 (   4.79kB)
    Total ROM Size (Code + RO Data + RW Data)      35272 (  34.45kB)

==============================================================================

