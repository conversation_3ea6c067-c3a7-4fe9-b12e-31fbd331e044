# 野火K210硬件配置文件
# 适配野火K210 AI视觉相机的引脚和参数配置

"""
野火K210引脚映射配置
根据野火K210硬件手册进行的引脚分配
"""

# 引脚组1配置 (28个引脚)
PIN_GROUP_1 = {
    # 电源引脚
    'VCC_3V3_1': 1,    # 3.3V电源
    'VCC_5V_1': 2,     # 5V电源
    'VCC_3V3_2': 17,   # 3.3V电源
    'GND_1': 9,        # 地线
    'GND_2': 14,       # 地线
    'GND_3': 20,       # 地线
    'GND_4': 27,       # 地线
    'GND_5': 28,       # 地线
    
    # 可用IO引脚
    'IO_36': 3,        # 可复用为UART_RX
    'IO_37': 5,        # 可复用为UART_TX
    'IO_38': 7,        # 可复用为PWM
    'IO_0': 8,         # 可复用为GPIO
    'IO_1': 10,        # 可复用为GPIO
    'IO_2': 12,        # 可复用为GPIO
    'IO_39': 11,       # 可复用为GPIO
    'IO_40': 13,       # 可复用为GPIO
    'IO_41': 15,       # 可复用为GPIO
    'IO_3': 16,        # 可复用为GPIO
    'IO_10': 18,       # 可复用为GPIO
    'IO_42': 19,       # 可复用为GPIO
    'IO_43': 21,       # 可复用为GPIO
    'IO_11': 22,       # 可复用为GPIO
    'IO_44': 23,       # 可复用为GPIO
    'IO_46': 24,       # 可复用为GPIO
    'IO_45': 25,       # 可复用为GPIO
    'IO_47': 26,       # 可复用为GPIO
}

# 引脚组2配置 (6个引脚)
PIN_GROUP_2 = {
    # 电源引脚
    'VCC_1V8': 1,      # 1.8V电源
    'GND': 6,          # 地线
    
    # 可用IO引脚
    'IO_32': 3,        # 可复用为I2C_SDA
    'IO_33': 5,        # 可复用为I2C_SCL
    'IO_34': 2,        # 可复用为GPIO
    'IO_35': 4,        # 可复用为GPIO
}

# 串口配置
UART_CONFIG = {
    'UART1': {
        'RX_PIN': 3,       # 引脚组1-引脚3 (IO_36)
        'TX_PIN': 5,       # 引脚组1-引脚5 (IO_37)
        'BAUDRATE': 115200,
        'READ_BUF_LEN': 4096
    }
}

# PWM配置 (用于舵机控制的备用方案)
PWM_CONFIG = {
    'SERVO1_PIN': 7,       # 引脚组1-引脚7 (IO_38)
    'SERVO2_PIN': 8,       # 引脚组1-引脚8 (IO_0)
    'FREQUENCY': 50,       # 50Hz PWM频率
    'DUTY_MIN': 2.5,       # 最小占空比
    'DUTY_MAX': 12.5,      # 最大占空比
}

# 摄像头配置
CAMERA_CONFIG = {
    'PIXFORMAT': 'GRAYSCALE',  # 灰度模式
    'FRAMESIZE': 'QQVGA',      # 160x120分辨率
    'BRIGHTNESS': -2,          # 亮度调节
    'SATURATION': -2,          # 饱和度调节
    'AUTO_GAIN': True,         # 自动增益
    'AUTO_WHITEBAL': False,    # 关闭自动白平衡
    'SKIP_FRAMES': 2000,       # 跳过帧数
}

# LCD配置
LCD_CONFIG = {
    'CLEAR_COLOR': 'WHITE',    # 清屏颜色
    'TEXT_COLOR': {
        'RED': (255, 0, 0),
        'GREEN': (0, 255, 0),
        'BLUE': (0, 0, 255),
        'WHITE': (255, 255, 255),
        'BLACK': (0, 0, 0),
    }
}

# 图像处理配置
IMAGE_PROCESSING = {
    'RECT_THRESHOLD': 30000,   # 矩形检测阈值
    'LAPLACIAN_SIZE': 1,       # 拉普拉斯核大小
    'SHARPEN': True,           # 锐化开关
    'TARGET_CENTER': (70, 70), # 目标中心位置
}

# 舵机控制配置
SERVO_CONFIG = {
    'ID1': 0x01,              # 舵机1 ID
    'ID2': 0x02,              # 舵机2 ID
    'INIT_POSITION': 2096,    # 初始位置
    'CENTER_POSITION': 2048,  # 中心位置
    'LEFT_POSITION': 1000,    # 左转位置
    'RIGHT_POSITION': 3000,   # 右转位置
    'INTERVAL': 1000,         # 运动时间间隔
}

# PID控制配置
PID_CONFIG = {
    'KP': 0,                  # 比例系数
    'KI': 1,                  # 积分系数
    'KD': 0,                  # 微分系数
    'TARGET_X': 70,           # X轴目标位置
    'TARGET_Y': 70,           # Y轴目标位置
}

# 系统配置
SYSTEM_CONFIG = {
    'MAIN_LOOP_DELAY': 0.001, # 主循环延迟
    'THETA_INCREMENT': 0.001, # 角度增量
    'GC_ENABLE': True,        # 垃圾回收开关
    'DEBUG_MODE': True,       # 调试模式
}

# 串口指令映射
UART_COMMANDS = {
    '2': 'CENTER',            # 回中位指令
    '3': 'LEFT',              # 左转指令
    '4': 'CENTER',            # 回中位指令
    '5': 'RIGHT',             # 右转指令
}

# 显示配置
DISPLAY_CONFIG = {
    'SHOW_FPS': True,         # 显示FPS
    'SHOW_STATUS': True,      # 显示状态信息
    'SHOW_CROSSHAIR': True,   # 显示十字线
    'TEXT_SCALE': 2,          # 文字缩放
    'CROSSHAIR_SIZE': 4,      # 十字线大小
}

# 错误处理配置
ERROR_CONFIG = {
    'UART_TIMEOUT': 1000,     # 串口超时时间(ms)
    'MAX_RETRY': 3,           # 最大重试次数
    'ERROR_LOG': True,        # 错误日志开关
}

# 性能优化配置
PERFORMANCE_CONFIG = {
    'DUAL_BUFF': True,        # 双缓冲开关
    'FREQ': 24000000,         # 摄像头频率
    'GC_THRESHOLD': 100,      # 垃圾回收阈值
}

# 导出所有配置
__all__ = [
    'PIN_GROUP_1', 'PIN_GROUP_2', 'UART_CONFIG', 'PWM_CONFIG',
    'CAMERA_CONFIG', 'LCD_CONFIG', 'IMAGE_PROCESSING', 'SERVO_CONFIG',
    'PID_CONFIG', 'SYSTEM_CONFIG', 'UART_COMMANDS', 'DISPLAY_CONFIG',
    'ERROR_CONFIG', 'PERFORMANCE_CONFIG'
]
