/*
 * oled.c
 *
 *  Created on: Jun 11, 2022
 *      Author: lf
 */
#include "bsp_oled.h"
#include "OLED_Font.h"
#include "bsp_systick.h"

/*引脚配置宏定义*/
#define OLED_W_SCL(x)  ((x) ? DL_GPIO_setPins(OLED_SCL_PORT,OLED_SCL_PIN) : DL_GPIO_clearPins(OLED_SCL_PORT,OLED_SCL_PIN))
#define OLED_W_SDA(x)  ((x) ? DL_GPIO_setPins(OLED_SDA_PORT,OLED_SDA_PIN) : DL_GPIO_clearPins(OLED_SDA_PORT,OLED_SDA_PIN))


/**
  * @brief  I2C开始
  * @param  无
  * @retval 无
  */
void OLED_I2C_Start(void)
{
	OLED_W_SDA(1);
	OLED_W_SCL(1);
	delay_us(10);
	OLED_W_SDA(0);
	delay_us(10);
	OLED_W_SCL(0);
	delay_us(10);
}

/**
  * @brief  I2C停止
  * @param  无
  * @retval 无
  */
void OLED_I2C_Stop(void)
{
	OLED_W_SDA(0);
	delay_us(10);
	OLED_W_SCL(1);
	delay_us(10);
	OLED_W_SDA(1);
	delay_us(10);
}

/**
  * @brief  I2C发送一个字节
  * @param  Byte 要发送的一个字节
  * @retval 无
  */
void OLED_I2C_SendByte(uint8_t Byte)
{
	uint8_t i;
	for (i = 0; i < 8; i++)
	{
		OLED_W_SDA(Byte & (0x80 >> i));
		delay_us(10);
		OLED_W_SCL(1);
		delay_us(10);
		OLED_W_SCL(0);
		delay_us(10);
	}
	OLED_W_SCL(1);	//额外的一个时钟，不处理应答信号
	delay_us(10);
	OLED_W_SCL(0);
}

/**
  * @brief  OLED写命令
  * @param  Command 要写入的命令
  * @retval 无
  */
void OLED_WriteCommand(uint8_t Command)
{
	OLED_I2C_Start();
	OLED_I2C_SendByte(0x78);		//从机地址
	OLED_I2C_SendByte(0x00);		//写命令
	OLED_I2C_SendByte(Command);
	OLED_I2C_Stop();
}

/**
  * @brief  OLED写数据
  * @param  Data 要写入的数据
  * @retval 无
  */
void OLED_WriteData(uint8_t Data)
{
	OLED_I2C_Start();
	OLED_I2C_SendByte(0x78);		//从机地址
	OLED_I2C_SendByte(0x40);		//写数据
	OLED_I2C_SendByte(Data);
	OLED_I2C_Stop();
}

/**
  * @brief  OLED设置光标位置
  * @param  Y 以左上角为原点，向下方向的坐标，范围：0~7
  * @param  X 以左上角为原点，向右方向的坐标，范围：0~127
  * @retval 无
  */
void OLED_SetCursor(uint8_t Y, uint8_t X)
{
	OLED_WriteCommand(0xB0 | Y);					//设置Y位置
	OLED_WriteCommand(0x10 | ((X & 0xF0) >> 4));	//设置X位置高4位
	OLED_WriteCommand(0x00 | (X & 0x0F));			//设置X位置低4位
}
/**
  * @brief  OLED清屏
  * @param  无
  * @retval 无
  */
void OLED_Clear(void)
{
	uint8_t i, j;
	for (j = 0; j < 8; j++)
	{
		OLED_SetCursor(j, 0);
		for(i = 0; i < 128; i++)
		{
			OLED_WriteData(0x00);
		}
	}
}
/**
  * @brief  OLED显示一个字符
  * @param  Line 行位置，范围：1~4
  * @param  Column 列位置，范围：1~16
  * @param  Char 要显示的一个字符，范围：ASCII可见字符
  * @retval 无
  */
void OLED_ShowChar(uint8_t Line, uint8_t Column, char Char)
{
	uint8_t i;
	OLED_SetCursor((Line - 1) * 2, (Column - 1) * 8);		//设置光标位置在上半部分
	for (i = 0; i < 8; i++)
	{
		OLED_WriteData(OLED_F8x16[Char - ' '][i]);			//显示上半部分内容
	}
	OLED_SetCursor((Line - 1) * 2 + 1, (Column - 1) * 8);	//设置光标位置在下半部分
	for (i = 0; i < 8; i++)
	{
		OLED_WriteData(OLED_F8x16[Char - ' '][i + 8]);		//显示下半部分内容
	}
}

/**
  * @brief  OLED显示字符串
  * @param  Line 起始行位置，范围：1~4
  * @param  Column 起始列位置，范围：1~16
  * @param  String 要显示的字符串，范围：ASCII可见字符
  * @retval 无
  */
void OLED_ShowString(uint8_t Line, uint8_t Column, char *String)
{
	uint8_t i;
	for (i = 0; String[i] != '\0'; i++)
	{
		OLED_ShowChar(Line, Column + i, String[i]);
	}
}

/**
  * @brief  OLED次方函数
  * @retval 返回值等于X的Y次方
  */
uint32_t OLED_Pow(uint32_t X, uint32_t Y)
{
	uint32_t Result = 1;
	while (Y--)
	{
		Result *= X;
	}
	return Result;
}
/**
  * @brief  OLED显示数字（十进制，正数）
  * @param  Line 起始行位置，范围：1~4
  * @param  Column 起始列位置，范围：1~16
  * @param  Number 要显示的数字，范围：0~4294967295
  * @param  Length 要显示数字的长度，范围：1~10
  * @retval 无
  */
void OLED_ShowNum(uint8_t Line, uint8_t Column, uint32_t Number, uint8_t Length)
{
	uint8_t i;
	for (i = 0; i < Length; i++)
	{
		OLED_ShowChar(Line, Column + i, Number / OLED_Pow(10, Length - i - 1) % 10 + '0');
	}
}

/**
  * @brief  OLED显示数字（十进制，带符号数）
  * @param  Line 起始行位置，范围：1~4
  * @param  Column 起始列位置，范围：1~16
  * @param  Number 要显示的数字，范围：-2147483648~2147483647
  * @param  Length 要显示数字的长度，范围：1~10
  * @retval 无
  */
void OLED_ShowSignedNum(uint8_t Line, uint8_t Column, int32_t Number, uint8_t Length)
{
	uint8_t i;
	uint32_t Number1;
	if (Number >= 0)
	{
		OLED_ShowChar(Line, Column, '+');
		Number1 = Number;
	}
	else
	{
		OLED_ShowChar(Line, Column, '-');
		Number1 = -Number;
	}
	for (i = 0; i < Length; i++)
	{
		OLED_ShowChar(Line, Column + i + 1, Number1 / OLED_Pow(10, Length - i - 1) % 10 + '0');
	}
}
/**
  * @brief  OLED初始化
  * @param  无
  * @retval 无
  */
void OLED_Init(void)
{
	uint32_t i, j;

	// 确保引脚正确初始化为输出模式
	OLED_W_SCL(1);  // SCL初始化为高电平
	OLED_W_SDA(1);  // SDA初始化为高电平

	for (i = 0; i < 1000; i++)			//上电延时
	{
		for (j = 0; j < 1000; j++);
	}

	// 标准SSD1306初始化序列
	OLED_WriteCommand(0xAE); // 关闭显示
	delay_ms(10);

	OLED_WriteCommand(0x20); // 设置内存地址模式
	OLED_WriteCommand(0x10); // 00,水平地址模式;01,垂直地址模式;10,页地址模式(RESET);11,无效

	OLED_WriteCommand(0xB0); // 设置页地址
	OLED_WriteCommand(0xC8); // 设置COM扫描方向
	OLED_WriteCommand(0x00); // 设置低列地址
	OLED_WriteCommand(0x10); // 设置高列地址
	OLED_WriteCommand(0x40); // 设置起始行地址

	OLED_WriteCommand(0x81); // 设置对比度
	OLED_WriteCommand(0xFF); // 最大亮度

	OLED_WriteCommand(0xA1); // 设置段重映射
	OLED_WriteCommand(0xA6); // 正常显示
	OLED_WriteCommand(0xA8); // 设置驱动路数
	OLED_WriteCommand(0x3F); // 1/64 duty

	OLED_WriteCommand(0xA4); // 全局显示开启
	OLED_WriteCommand(0xD3); // 设置显示偏移
	OLED_WriteCommand(0x00); // 无偏移

	OLED_WriteCommand(0xD5); // 设置时钟分频因子
	OLED_WriteCommand(0xF0); // 分频因子

	OLED_WriteCommand(0xD9); // 设置预充电周期
	OLED_WriteCommand(0x22);

	OLED_WriteCommand(0xDA); // 设置COM硬件引脚配置
	OLED_WriteCommand(0x12);

	OLED_WriteCommand(0xDB); // 设置VCOMH
	OLED_WriteCommand(0x20); // 0.77*VCC

	OLED_WriteCommand(0x8D); // 电荷泵设置
	OLED_WriteCommand(0x14); // 开启电荷泵

	delay_ms(100);
	OLED_WriteCommand(0xAF); // 开启显示
	delay_ms(100);

	OLED_Clear();
}



// 兼容原有接口的包装函数
static void OLED_ShowChar_Wrapper(uint8_t x, uint8_t y, uint8_t chr, uint8_t size, uint8_t mode)
{
    // 正确的坐标转换：OLED只支持1~4行，1~16列
    // x: 0-127像素 -> 1-16列 (每列8像素)
    // y: 0-63像素 -> 1-4行 (每行16像素)

    uint8_t line = (y / 16) + 1;    // y坐标转换为行（每行16像素高度）
    uint8_t column = (x / 8) + 1;   // x坐标转换为列（每列8像素宽度）

    // 边界检查 - 只支持4行16列
    if (line > 4) line = 4;         // 最多4行
    if (column > 16) column = 16;   // 最多16列
    if (line < 1) line = 1;
    if (column < 1) column = 1;

    OLED_ShowChar(line, column, chr);
}

static void OLED_ShowNumber_Wrapper(uint8_t x, uint8_t y, uint32_t num, uint8_t len, uint8_t size)
{
    // 与字符显示使用相同的坐标转换
    uint8_t line = (y / 16) + 1;    // 每行16像素高度
    uint8_t column = (x / 8) + 1;   // 每列8像素宽度

    // 边界检查 - 只支持4行16列
    if (line > 4) line = 4;
    if (column > 16) column = 16;
    if (line < 1) line = 1;
    if (column < 1) column = 1;

    OLED_ShowNum(line, column, num, len);
}

static void OLED_ShowString_Wrapper(uint8_t x, uint8_t y, const char *p)
{
    // 与字符显示使用相同的坐标转换
    uint8_t line = (y / 16) + 1;    // 每行16像素高度
    uint8_t column = (x / 8) + 1;   // 每列8像素宽度

    // 边界检查 - 只支持4行16列
    if (line > 4) line = 4;
    if (column > 16) column = 16;
    if (line < 1) line = 1;
    if (column < 1) column = 1;

    OLED_ShowString(line, column, (char*)p);
}

static void OLED_ShowFloat_Wrapper(uint8_t x, uint8_t y, const float num, uint8_t zs_num, uint8_t xs_num)
{
    // 完整的浮点数显示实现
    uint8_t line = (y / 16) + 1;    // 每行16像素高度
    uint8_t column = (x / 8) + 1;   // 每列8像素宽度

    // 边界检查 - 只支持4行16列
    if (line > 4) line = 4;
    if (column > 16) column = 16;
    if (line < 1) line = 1;
    if (column < 1) column = 1;

    int32_t int_part;
    uint32_t frac_part;
    uint8_t pos = 0;

    // 处理符号
    if (num >= 0) {
        OLED_ShowChar(line, column + pos, '+');
        int_part = (int32_t)num;
        frac_part = (uint32_t)((num - int_part) * 1000); // 取3位小数
    } else {
        OLED_ShowChar(line, column + pos, '-');
        int_part = (int32_t)(-num);
        frac_part = (uint32_t)((-num - int_part) * 1000); // 取3位小数
    }
    pos++;

    // 显示整数部分
    for (uint8_t i = 0; i < zs_num; i++) {
        OLED_ShowChar(line, column + pos + i, (int_part / OLED_Pow(10, zs_num - i - 1)) % 10 + '0');
    }
    pos += zs_num;

    // 显示小数点
    OLED_ShowChar(line, column + pos, '.');
    pos++;

    // 显示小数部分
    for (uint8_t i = 0; i < xs_num; i++) {
        OLED_ShowChar(line, column + pos + i, (frac_part / OLED_Pow(10, xs_num - i - 1)) % 10 + '0');
    }
}

static void OLED_RefreshGram_Wrapper(void)
{
    // 新的OLED驱动不需要刷新缓存，直接写入显示
    // 这个函数为空，保持兼容性
}

//驱动挂载 - 保持与原代码的兼容性
OLEDInterface_t UserOLED = {
	.init = OLED_Init,
	.ShowChar = OLED_ShowChar_Wrapper,
	.ShowNumber = OLED_ShowNumber_Wrapper,
	.ShowString = OLED_ShowString_Wrapper,
	.ShowFloat = OLED_ShowFloat_Wrapper,
	.RefreshGram = OLED_RefreshGram_Wrapper,
	.Clear = OLED_Clear
};

