# 野火K210工具函数库
# 提供通用的工具函数和辅助功能

import time
import math
from machine import UART, Timer, PWM
from fpioa_manager import fm
import sensor, image, lcd
import gc

class WildFireK210Utils:
    """野火K210工具类"""
    
    def __init__(self):
        self.uart = None
        self.clock = None
        self.servo_positions = {}
        
    def init_uart(self, rx_pin=3, tx_pin=5, baudrate=115200):
        """初始化串口通信"""
        try:
            fm.register(rx_pin, fm.fpioa.UART1_RX, force=True)
            fm.register(tx_pin, fm.fpioa.UART1_TX, force=True)
            self.uart = UART(UART.UART1, baudrate, read_buf_len=4096)
            print(f"串口初始化成功: RX={rx_pin}, TX={tx_pin}, 波特率={baudrate}")
            return True
        except Exception as e:
            print(f"串口初始化失败: {e}")
            return False
    
    def init_camera(self, pixformat='GRAYSCALE', framesize='QQVGA'):
        """初始化摄像头"""
        try:
            sensor.reset(dual_buff=True)
            sensor.reset(freq=24000000, dual_buff=1)
            
            if pixformat == 'GRAYSCALE':
                sensor.set_pixformat(sensor.GRAYSCALE)
            else:
                sensor.set_pixformat(sensor.RGB565)
                
            if framesize == 'QQVGA':
                sensor.set_framesize(sensor.QQVGA)
            elif framesize == 'QVGA':
                sensor.set_framesize(sensor.QVGA)
            
            sensor.set_brightness(-2)
            sensor.set_saturation(-2)
            sensor.set_auto_gain(True)
            sensor.set_auto_whitebal(False)
            sensor.skip_frames(time=2000)
            
            print(f"摄像头初始化成功: {pixformat}, {framesize}")
            return True
        except Exception as e:
            print(f"摄像头初始化失败: {e}")
            return False
    
    def init_lcd(self, clear_color='WHITE'):
        """初始化LCD显示"""
        try:
            lcd.init()
            if clear_color == 'WHITE':
                lcd.clear(lcd.WHITE)
            elif clear_color == 'BLACK':
                lcd.clear(lcd.BLACK)
            else:
                lcd.clear(lcd.WHITE)
            
            print(f"LCD初始化成功: 清屏颜色={clear_color}")
            return True
        except Exception as e:
            print(f"LCD初始化失败: {e}")
            return False
    
    def servo_control(self, servo_id, position, interval=1000):
        """舵机控制函数"""
        try:
            ZT1 = 0xFF
            ZT2 = 0xFF
            DATA1 = 0X2A
            DATA2 = (position >> 8) & 0xff
            DATA3 = position & 0xff
            DATA4 = (interval >> 8) & 0xff
            DATA5 = interval & 0xff
            data_length = 0x07
            WriteDATA = 0X03
            GetChecksum = (~(servo_id + data_length + WriteDATA + DATA1 + DATA2 + DATA3 + DATA4 + DATA5)) & 0xff
            
            command = bytes([ZT1, ZT2, servo_id, data_length, WriteDATA, DATA1, DATA2, DATA3, DATA4, DATA5, GetChecksum])
            
            if self.uart:
                self.uart.write(command)
                self.servo_positions[servo_id] = position
                return True
            else:
                print("串口未初始化")
                return False
        except Exception as e:
            print(f"舵机控制失败: {e}")
            return False
    
    def read_uart_command(self):
        """读取串口指令"""
        try:
            if self.uart:
                data = self.uart.read(1)
                if data:
                    return data.decode('utf-8')
            return None
        except Exception as e:
            print(f"串口读取失败: {e}")
            return None
    
    def detect_rectangles(self, img, threshold=30000):
        """检测图像中的矩形"""
        try:
            # 图像预处理
            img.laplacian(1, sharpen=True)
            
            rectangles = []
            for r in img.find_rects(threshold=threshold):
                # 绘制矩形框
                img.draw_rectangle(r.rect(), color=(255, 0, 0))
                
                # 绘制角点
                for p in r.corners():
                    img.draw_circle(p[0], p[1], 5, color=(0, 255, 0))
                
                # 计算中心点
                corners = r.corners()
                center_x = sum(p[0] for p in corners) / 4
                center_y = sum(p[1] for p in corners) / 4
                
                rectangles.append({
                    'rect': r.rect(),
                    'corners': corners,
                    'center': (center_x, center_y)
                })
            
            return rectangles
        except Exception as e:
            print(f"矩形检测失败: {e}")
            return []
    
    def calculate_pid(self, current_x, current_y, target_x=70, target_y=70, kp=0, ki=1, kd=0):
        """PID控制计算"""
        try:
            # 计算误差
            error_x = target_x - current_x
            error_y = target_y - current_y
            
            # PID计算 (简化版本，只使用积分项)
            pid_x = error_x * ki
            pid_y = error_y * ki
            
            return pid_x, pid_y
        except Exception as e:
            print(f"PID计算失败: {e}")
            return 0, 0
    
    def draw_status_info(self, img, fps=0, status="Running"):
        """绘制状态信息"""
        try:
            # 绘制FPS
            img.draw_string(0, 0, f"FPS: {fps:.1f}", color=(255, 0, 0), scale=2)
            
            # 绘制设备信息
            img.draw_string(0, 20, "WildFire K210", color=(0, 255, 0), scale=2)
            
            # 绘制状态
            img.draw_string(0, 40, status, color=(0, 0, 255), scale=2)
            
            # 绘制中心十字线
            img_width = img.width()
            img_height = img.height()
            center_x = img_width // 2
            center_y = img_height // 2
            img.draw_cross(center_x, center_y, color=(255, 0, 0), scale=4)
            
        except Exception as e:
            print(f"状态信息绘制失败: {e}")
    
    def process_uart_commands(self, command, servo1_id=0x01, servo2_id=0x02):
        """处理串口指令"""
        command_map = {
            '2': 2048,  # 中心位置
            '3': 1000,  # 左转位置
            '4': 2048,  # 中心位置
            '5': 3000,  # 右转位置
        }
        
        if command in command_map:
            position = command_map[command]
            self.servo_control(servo1_id, position)
            self.servo_control(servo2_id, 2025)  # 垂直轴保持固定位置
            return True
        return False
    
    def memory_management(self):
        """内存管理"""
        try:
            gc.collect()
            # 可以添加内存使用情况监控
        except Exception as e:
            print(f"内存管理失败: {e}")
    
    def system_test(self):
        """系统自检"""
        print("=== 野火K210系统自检 ===")
        
        # 测试LCD
        lcd_ok = self.init_lcd()
        print(f"LCD测试: {'通过' if lcd_ok else '失败'}")
        
        # 测试摄像头
        camera_ok = self.init_camera()
        print(f"摄像头测试: {'通过' if camera_ok else '失败'}")
        
        # 测试串口
        uart_ok = self.init_uart()
        print(f"串口测试: {'通过' if uart_ok else '失败'}")
        
        # 测试舵机控制
        servo_ok = self.servo_control(0x01, 2048)
        print(f"舵机测试: {'通过' if servo_ok else '失败'}")
        
        all_ok = lcd_ok and camera_ok and uart_ok and servo_ok
        print(f"系统自检: {'全部通过' if all_ok else '存在问题'}")
        print("========================")
        
        return all_ok

class PerformanceMonitor:
    """性能监控类"""
    
    def __init__(self):
        self.frame_count = 0
        self.start_time = time.time()
        self.fps_history = []
    
    def update(self):
        """更新性能数据"""
        self.frame_count += 1
        current_time = time.time()
        elapsed = current_time - self.start_time
        
        if elapsed >= 1.0:  # 每秒计算一次FPS
            fps = self.frame_count / elapsed
            self.fps_history.append(fps)
            
            # 保持历史记录在合理范围内
            if len(self.fps_history) > 10:
                self.fps_history.pop(0)
            
            self.frame_count = 0
            self.start_time = current_time
            
            return fps
        return 0
    
    def get_average_fps(self):
        """获取平均FPS"""
        if self.fps_history:
            return sum(self.fps_history) / len(self.fps_history)
        return 0

# 创建全局工具实例
wildfire_utils = WildFireK210Utils()
performance_monitor = PerformanceMonitor()

# 导出主要功能
__all__ = ['WildFireK210Utils', 'PerformanceMonitor', 'wildfire_utils', 'performance_monitor']
