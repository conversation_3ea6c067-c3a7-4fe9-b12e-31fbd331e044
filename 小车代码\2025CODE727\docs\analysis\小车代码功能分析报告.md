# 小车代码功能分析报告

## 项目概述

这是一个基于TI MSPM0G3507微控制器的**自平衡小车**项目，具备**循迹功能**。项目使用Keil开发环境，采用TI DriverLib库进行硬件抽象。

## 核心功能模块

### 1. 自平衡控制系统
- **传感器**: MPU6050六轴陀螺仪+加速度计，使用DMP（Digital Motion Processor）进行姿态解算
- **控制算法**: PID控制器，包含平衡环、速度环、转向环
- **控制频率**: 200Hz（5ms执行一次控制任务）
- **电机控制**: 双路PWM输出控制左右电机

### 2. 循迹系统
- **传感器**: 6路红外传感器阵列（HD0-HD5）
- **算法**: 基于加权评分的循迹算法
- **功能**: 
  - 实时检测黑线位置
  - 计算转向偏差
  - 支持多种循迹模式（xuanti变量控制）

### 3. 编码器反馈系统
- **编码器**: 双路正交编码器（左右轮各一个）
- **功能**: 
  - 实时测量车轮转速
  - 提供速度反馈给控制系统
  - 支持2倍频解码

### 4. 显示与交互系统
- **显示**: OLED显示屏，显示实时状态信息
- **按键**: KEY0按键用于模式切换
- **启动开关**: HD6作为启动/停止控制

## 硬件架构

### 主控制器
- **MCU**: TI MSPM0G3507 (ARM Cortex-M0+)
- **主频**: 80MHz
- **开发板**: LP-MSPM0G3507 LaunchPad

### 传感器配置
```
MPU6050 (I2C接口):
- 地址: 0x68
- 中断引脚: 配置为下降沿触发
- DMP功能: 启用，输出欧拉角

红外传感器阵列:
- HD0-HD5: 6路数字输入
- 用途: 循迹检测

编码器:
- Encoder1: 左轮编码器 (A/B相)
- Encoder2: 右轮编码器 (A/B相)
- 中断方式: 边沿触发，2倍频解码
```

### 执行器配置
```
电机驱动:
- PWM_0通道0: 左电机控制 (PB2)
- PWM_0通道1: 右电机控制 (PB3)
- PWM频率: 由80MHz时钟分频得到

OLED显示:
- 接口: I2C
- 分辨率: 128x64
```

## 软件架构

### 主程序流程
1. **初始化阶段**:
   - 系统时钟配置
   - 外设初始化（I2C、UART、PWM、ADC）
   - MPU6050和DMP初始化
   - 中断配置

2. **主循环**:
   - 蓝牙数据处理（已注释）
   - 循迹逻辑处理
   - 状态显示更新（100ms周期）
   - 模式切换处理

### 中断服务程序
```c
GROUP1_IRQHandler(): 
- 编码器中断处理（2倍频解码）
- MPU6050数据就绪中断
- 平衡控制任务调用（5ms周期）
```

### 控制算法详解

#### 平衡控制（Balance函数）
```c
// PD控制器
balance = -Balance_Kp/100 * Angle_bias - Gyro_bias * Balance_Kd/100
```

#### 速度控制（Velocity函数）
```c
// PI控制器
velocity = -Encoder_bias * Velocity_Kp/100 - Encoder_Integral * Velocity_Ki/100
```

#### 转向控制（Turn函数）
```c
// PD控制器
turn = Turn_Target * Kp/100 + gyro * Kd/100
```

### 循迹算法
```c
float xunji(void) {
    float score = 0;
    // 加权评分算法
    if (HD0 > 0) score += 15;   // 最左侧
    if (HD1 > 0) score += 10;   // 左侧
    if (HD2 > 0) score += 4;    // 中左
    if (HD3 > 0) score += -4;   // 中右
    if (HD4 > 0) score += -10;  // 右侧
    if (HD5 > 0) score += -15;  // 最右侧
    return score;
}
```

## 发现的问题

### 1. 代码结构问题
- **全局变量过多**: 大量全局变量缺乏封装，增加维护难度
- **魔法数字**: 代码中存在大量硬编码数值，缺乏宏定义
- **注释不一致**: 部分代码有中英文混合注释

### 2. 算法实现问题
- **PID参数**: 当前PID参数大部分为0，可能导致控制效果不佳
```c
float Balance_Kp=0,Balance_Kd=0,Velocity_Kp=-5,Velocity_Ki=0,Turn_Kp=10,Turn_Kd=0;
```

### 3. 功能完整性问题
- **蓝牙功能**: 相关代码被注释，功能不完整
- **电压检测**: 虽有电压检测代码，但保护逻辑被注释
- **错误处理**: 缺乏完善的错误处理机制

### 4. 硬件兼容性
- **I2C速度**: 注释提到软件I2C速度不够，可能影响DMP读取
- **中断优先级**: 未见明确的中断优先级配置

## 建议改进方向

### 1. 代码重构
- 将全局变量封装到结构体中
- 定义宏常量替换魔法数字
- 统一代码注释风格

### 2. 算法优化
- 重新调试PID参数
- 增加自适应控制算法
- 优化循迹算法的响应速度

### 3. 功能完善
- 恢复蓝牙通信功能
- 完善安全保护机制
- 增加故障诊断功能

### 4. 性能优化
- 优化I2C通信速度
- 合理配置中断优先级
- 减少不必要的计算开销

## 总结

这个小车项目实现了基本的自平衡和循迹功能，硬件配置合理，软件架构基本清晰。但在代码质量、算法调优和功能完整性方面还有较大改进空间。建议优先解决PID参数调试和代码结构优化问题。
