#ifndef __BSP_OLED_H
#define __BSP_OLED_H

#include "ti_msp_dl_config.h"

// 标准OLED函数声明
void OLED_Init(void);
void OLED_Clear(void);
void OLED_ShowChar(uint8_t Line, uint8_t Column, char Char);
void OLED_ShowString(uint8_t Line, uint8_t Column, char *String);
void OLED_ShowNum(uint8_t Line, uint8_t Column, uint32_t Number, uint8_t Length);
void OLED_ShowSignedNum(uint8_t Line, uint8_t Column, int32_t Number, uint8_t Length);
uint32_t OLED_Pow(uint32_t X, uint32_t Y);

// 兼容性接口结构体
typedef struct {
	void (*init)(void);
    void (*ShowChar)(uint8_t x,uint8_t y,uint8_t chr,uint8_t size,uint8_t mode);
    void (*ShowNumber)(uint8_t x,uint8_t y,uint32_t num,uint8_t len,uint8_t size);
    void (*ShowString)(uint8_t x,uint8_t y,const char *p);
	void (*ShowFloat)(uint8_t show_x,uint8_t show_y,const float needtoshow,uint8_t zs_num,uint8_t xs_num);
	void (*RefreshGram)(void);
	void (*Clear)(void);
}OLEDInterface_t,*pOLEDInterface_t;

extern OLEDInterface_t UserOLED;

#endif
	 
