# 野火K210基础功能测试
# 用于验证硬件和基础功能是否正常工作

import sys
sys.path.append('..')

from utils import wildfire_utils, performance_monitor
import time
import sensor, image, lcd

def test_lcd():
    """测试LCD显示功能"""
    print("=== LCD显示测试 ===")
    
    try:
        # 初始化LCD
        lcd.init()
        
        # 测试不同颜色清屏
        colors = [lcd.WHITE, lcd.BLACK, lcd.RED, lcd.GREEN, lcd.BLUE]
        color_names = ["白色", "黑色", "红色", "绿色", "蓝色"]
        
        for color, name in zip(colors, color_names):
            print(f"显示{name}...")
            lcd.clear(color)
            time.sleep(1)
        
        # 恢复白色背景
        lcd.clear(lcd.WHITE)
        print("LCD测试完成")
        return True
        
    except Exception as e:
        print(f"LCD测试失败: {e}")
        return False

def test_camera():
    """测试摄像头功能"""
    print("=== 摄像头测试 ===")
    
    try:
        # 初始化摄像头
        sensor.reset(dual_buff=True)
        sensor.reset(freq=24000000, dual_buff=1)
        sensor.set_pixformat(sensor.GRAYSCALE)
        sensor.set_framesize(sensor.QQVGA)
        sensor.set_brightness(-2)
        sensor.set_saturation(-2)
        sensor.skip_frames(time=2000)
        
        print("摄像头初始化完成")
        
        # 测试图像采集
        test_frames = 10
        for i in range(test_frames):
            img = sensor.snapshot()
            print(f"采集第{i+1}帧图像: {img.width()}x{img.height()}")
            time.sleep(0.1)
        
        print("摄像头测试完成")
        return True
        
    except Exception as e:
        print(f"摄像头测试失败: {e}")
        return False

def test_uart():
    """测试串口通信"""
    print("=== 串口通信测试 ===")
    
    try:
        # 使用工具类初始化串口
        uart_ok = wildfire_utils.init_uart(rx_pin=3, tx_pin=5, baudrate=115200)
        
        if uart_ok:
            print("串口初始化成功")
            
            # 测试发送数据
            test_data = b"WildFire K210 Test"
            wildfire_utils.uart.write(test_data)
            print(f"发送测试数据: {test_data}")
            
            # 测试接收数据
            print("等待接收数据...")
            for i in range(5):
                data = wildfire_utils.read_uart_command()
                if data:
                    print(f"接收到数据: {data}")
                else:
                    print("未接收到数据")
                time.sleep(1)
            
            print("串口测试完成")
            return True
        else:
            print("串口初始化失败")
            return False
            
    except Exception as e:
        print(f"串口测试失败: {e}")
        return False

def test_servo():
    """测试舵机控制"""
    print("=== 舵机控制测试 ===")
    
    try:
        # 确保串口已初始化
        if not wildfire_utils.uart:
            wildfire_utils.init_uart()
        
        # 测试舵机位置
        positions = [1000, 1500, 2048, 2500, 3000, 2048]
        position_names = ["最左", "左偏", "中心", "右偏", "最右", "回中"]
        
        for pos, name in zip(positions, position_names):
            print(f"舵机移动到{name}位置: {pos}")
            wildfire_utils.servo_control(0x01, pos, 1000)
            wildfire_utils.servo_control(0x02, 2025, 1000)
            time.sleep(2)
        
        print("舵机测试完成")
        return True
        
    except Exception as e:
        print(f"舵机测试失败: {e}")
        return False

def test_image_processing():
    """测试图像处理功能"""
    print("=== 图像处理测试 ===")
    
    try:
        # 初始化摄像头和LCD
        wildfire_utils.init_camera()
        wildfire_utils.init_lcd()
        
        print("开始图像处理测试，持续10秒...")
        
        clock = time.clock()
        start_time = time.time()
        
        while time.time() - start_time < 10:
            clock.tick()
            
            # 采集图像
            img = sensor.snapshot()
            
            # 检测矩形
            rectangles = wildfire_utils.detect_rectangles(img, threshold=30000)
            
            # 绘制状态信息
            fps = clock.fps()
            wildfire_utils.draw_status_info(img, fps, f"检测到{len(rectangles)}个矩形")
            
            # 显示图像
            lcd.display(img)
            
            if rectangles:
                print(f"检测到{len(rectangles)}个矩形目标")
        
        print("图像处理测试完成")
        return True
        
    except Exception as e:
        print(f"图像处理测试失败: {e}")
        return False

def test_performance():
    """测试系统性能"""
    print("=== 系统性能测试 ===")
    
    try:
        # 初始化所有组件
        wildfire_utils.init_camera()
        wildfire_utils.init_lcd()
        wildfire_utils.init_uart()
        
        print("开始性能测试，持续30秒...")
        
        clock = time.clock()
        start_time = time.time()
        frame_count = 0
        
        while time.time() - start_time < 30:
            clock.tick()
            
            # 图像处理
            img = sensor.snapshot()
            img.laplacian(1, sharpen=True)
            
            # 矩形检测
            rectangles = img.find_rects(threshold=30000)
            
            # 绘制信息
            fps = clock.fps()
            img.draw_string(0, 0, f"FPS: {fps:.1f}", color=(255, 0, 0), scale=2)
            img.draw_string(0, 20, f"Frame: {frame_count}", color=(0, 255, 0), scale=2)
            
            # 显示
            lcd.display(img)
            
            # 内存管理
            if frame_count % 100 == 0:
                wildfire_utils.memory_management()
            
            frame_count += 1
        
        avg_fps = frame_count / 30
        print(f"性能测试完成")
        print(f"总帧数: {frame_count}")
        print(f"平均FPS: {avg_fps:.2f}")
        print(f"处理时间: 30秒")
        
        return True
        
    except Exception as e:
        print(f"性能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("野火K210基础功能测试开始")
    print("=" * 40)
    
    # 系统自检
    system_ok = wildfire_utils.system_test()
    
    if not system_ok:
        print("系统自检失败，请检查硬件连接")
        return
    
    # 逐项测试
    tests = [
        ("LCD显示", test_lcd),
        ("摄像头", test_camera),
        ("串口通信", test_uart),
        ("舵机控制", test_servo),
        ("图像处理", test_image_processing),
        ("系统性能", test_performance),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n开始{test_name}测试...")
        try:
            result = test_func()
            results[test_name] = result
            print(f"{test_name}测试: {'通过' if result else '失败'}")
        except Exception as e:
            print(f"{test_name}测试异常: {e}")
            results[test_name] = False
    
    # 测试结果汇总
    print("\n" + "=" * 40)
    print("测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！野火K210系统工作正常。")
    else:
        print("⚠️  部分测试失败，请检查相关硬件和配置。")

if __name__ == "__main__":
    main()
