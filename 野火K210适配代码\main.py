# 野火K210适配版本 - 基于01Studio代码移植
# 适配野火K210 AI视觉相机硬件平台

from machine import Timer, PWM, UART
import time
from math import sqrt, pi
import math
import sensor, image, lcd
from fpioa_manager import fm
from board import board_info
import gc

# 硬件初始化配置
def hardware_init():
    """野火K210硬件初始化"""
    
    # LCD初始化 - 野火K210标准配置
    lcd.init()
    lcd.clear(lcd.WHITE)
    
    # 摄像头初始化 - 适配野火K210
    sensor.reset(dual_buff=True)
    sensor.reset(freq=24000000, dual_buff=1)
    sensor.set_pixformat(sensor.GRAYSCALE)  # 灰度模式，提高处理速度
    sensor.set_framesize(sensor.QQVGA)     # QQVGA分辨率 (160x120)
    sensor.set_brightness(-2)
    sensor.set_saturation(-2)
    sensor.set_auto_gain(True)
    sensor.set_auto_whitebal(False)
    sensor.skip_frames(time=2000)
    
    print("野火K210硬件初始化完成")

# 舵机控制函数 - 适配野火K210引脚
def func_servo(id0, posit0, interval0):
    """舵机控制函数 - 使用野火K210串口协议"""
    ZT1 = 0xFF
    ZT2 = 0xFF
    DATA1 = 0X2A
    DATA2 = (posit0 >> 8) & 0xff
    DATA3 = posit0 & 0xff
    DATA4 = (interval0 >> 8) & 0xff
    DATA5 = interval0 & 0xff
    data_length = 0x07
    WriteDATA = 0X03
    GetChecksum = (~(id0 + data_length + WriteDATA + DATA1 + DATA2 + DATA3 + DATA4 + DATA5)) & 0xff
    text = bytes([ZT1, ZT2, id0, data_length, WriteDATA, DATA1, DATA2, DATA3, DATA4, DATA5, GetChecksum])
    uart.write(text)

# 主程序配置
def main():
    """主程序入口"""
    global uart, ZX, ZY, xin, yin, P, P2, I, I2, PI, PI2
    global state, text2, text2_last, condition, black
    global intiposit, posit, positx, interval, ID1, ID2, theta
    
    # 硬件初始化
    hardware_init()
    
    # 变量初始化
    clock = time.clock()
    ZX = 70
    xin = 70
    ZY = 70
    yin = 70
    P = 0
    P2 = 0
    I = 0
    I2 = 0
    PI = 0
    PI2 = 0
    state = 0
    text2 = 0
    text2_last = 0
    condition = 1
    black = (150, 255)
    
    # 舵机参数初始化
    intiposit = 2096
    posit = 2025
    positx = 2048
    interval = 1000
    ID1 = 0x01
    ID2 = 0x02
    theta = 0
    
    # 串口引脚映射 - 适配野火K210引脚组1
    # 使用引脚组1的IO_36(引脚3)作为RX，IO_37(引脚5)作为TX
    fm.register(3, fm.fpioa.UART1_RX, force=True)   # 引脚组1-引脚3 (IO_36)
    fm.register(5, fm.fpioa.UART1_TX, force=True)   # 引脚组1-引脚5 (IO_37)
    
    # 初始化串口 - 野火K210配置
    uart = UART(UART.UART1, 115200, read_buf_len=4096)
    
    # 舵机初始位置设置
    func_servo(ID1, int(positx), interval)
    func_servo(ID2, int(posit), interval)
    time.sleep(1)
    
    print("野火K210系统初始化完成，开始主循环...")
    
    # 主循环
    while True:
        # 垃圾回收
        gc.collect()
        
        # 时钟更新
        clock.tick()
        
        # 图像采集
        img = sensor.snapshot()
        
        # 串口数据读取
        text = uart.read(1)
        if text:
            try:
                text2 = text.decode('utf-8')
            except:
                text2 = 0
        
        # 图像处理 - 拉普拉斯边缘检测
        img.laplacian(1, sharpen=True)
        
        # 矩形检测
        for r in img.find_rects(threshold=30000):
            img.draw_rectangle(r.rect(), color=(255, 0, 0))
            for p in r.corners():
                img.draw_circle(p[0], p[1], 5, color=(0, 255, 0))
            
            # 计算矩形中心
            cor = r.corners()
            ZX = (cor[0][0] + cor[1][0] + cor[2][0] + cor[3][0]) / 4
            ZY = (cor[0][1] + cor[1][1] + cor[2][1] + cor[3][1]) / 4
        
        # PID控制计算
        P = (ZX - xin) * 0
        P2 = (ZY - yin) * 0
        I = (ZX - xin) * 1 + I
        I2 = (ZY - yin) * 1 + I2
        PI = P + I
        PI2 = P2 + I2
        
        # 周期性运动
        theta = theta + 0.001
        if theta >= 2 * pi:
            theta = 0
        
        # 串口指令处理
        if text2 != text2_last:
            state = 1
        text2_last = text2
        
        if state == 1:
            if text2 == '2':  # 回中位
                positx = 2048
                PI = 0
                PI2 = 0
                func_servo(ID1, int(positx - PI), interval)
                func_servo(ID2, int(posit - PI2), interval)
                time.sleep(1)
                state = 0
            elif text2 == '3':  # 左转
                positx = 1000
                PI = 0
                PI2 = 0
                func_servo(ID1, int(positx - PI), interval)
                func_servo(ID2, int(posit - PI2), interval)
                time.sleep(1)
                state = 0
            elif text2 == '4':  # 回中位
                positx = 2048
                PI = 0
                PI2 = 0
                func_servo(ID1, int(positx - PI), interval)
                func_servo(ID2, int(posit - PI2), interval)
                time.sleep(1)
                state = 0
            elif text2 == '5':  # 右转
                positx = 3000
                PI = 0
                PI2 = 0
                func_servo(ID1, int(positx - PI), interval)
                func_servo(ID2, int(posit - PI2), interval)
                time.sleep(1)
                state = 0
        
        # 实时舵机控制
        func_servo(ID1, int(positx - PI), interval)
        func_servo(ID2, int(posit - PI2), interval)
        
        # 显示FPS和状态信息
        fps = clock.fps()
        img.draw_string(0, 0, "FPS: %2.1f" % fps, color=(255, 0, 0), scale=2)
        img.draw_string(0, 20, "WildFire K210", color=(0, 255, 0), scale=2)
        img.draw_string(0, 40, "Target Track", color=(0, 0, 255), scale=2)
        
        # 绘制中心十字线
        img.draw_cross(80, 60, color=(255, 0, 0), scale=4)
        
        # LCD显示
        lcd.display(img)

# 程序入口
if __name__ == "__main__":
    main()
