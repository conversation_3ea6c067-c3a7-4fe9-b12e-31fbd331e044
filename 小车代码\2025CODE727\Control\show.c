#include "show.h"
#include "bsp_oled.h"
#include "balance.h"  // 引入balance.h获取电机和电压变量

//OLED接口
static pOLEDInterface_t oled = &UserOLED;
extern int xuanti;
//平衡任务的频率
extern uint8_t mainTaskFreq;

//平衡任务耗时
extern float mainTaskUseTime;
extern int i;
extern int openmv[10];
extern int x,y,state ;
extern	int16_t data1;
extern	int Encoder_Left,Encoder_Right,KEY0,HD0,HD1,HD2,HD3,HD4,HD5,HD6,HD7,gy,fx,time;
extern float m6050init;
int lfx=50;
void oled_show_test(void)
{
    // 测试4行显示，使用正确的坐标
    oled->Clear();

    // 使用正确的y坐标：每行16像素间距
    oled->ShowString(0, 0, "Line1: OK");     // 第1行 (y=0)
    oled->ShowString(0, 16, "Line2: OK");    // 第2行 (y=16)
    oled->ShowString(0, 32, "Line3: OK");    // 第3行 (y=32)
    oled->ShowString(0, 48, "Line4: OK");    // 第4行 (y=48)

    oled->RefreshGram();
}

void oled_show_simple(void)
{
    // 简化的显示函数，只显示基本状态
    static uint32_t last_time = 0;
    static uint8_t last_flag_stop = 255; // 初始化为无效值
    static uint8_t init_display = 0;

    // 只在第一次或需要时清屏
    if (!init_display) {
        oled->Clear();

        // 显示固定内容
        oled->ShowString(0, 0, "Balance Car");
        oled->ShowString(0, 12, "HD:");
        oled->ShowString(0, 24, "T:");
        oled->ShowString(60, 24, "    "); // 预留状态位置
        oled->ShowString(0, 36, "Only OLED+MPU");

        init_display = 1;
    }

    // 只更新变化的内容
    if (time != last_time) {
        // 清除旧的时间显示
        oled->ShowString(18, 24, "   ");
        // 显示新的时间
        oled->ShowNumber(18, 24, time % 1000, 3, 12);
        last_time = time;
    }

    // 只在状态改变时更新
    if (Flag_Stop != last_flag_stop) {
        if(Flag_Stop)
            oled->ShowString(60, 24, "STOP");
        else
            oled->ShowString(60, 24, "RUN ");
        last_flag_stop = Flag_Stop;
    }

    // 显示传感器状态（这些可能会变化）
    oled->ShowNumber(24, 12, HD0, 1, 12);
    oled->ShowNumber(36, 12, HD1, 1, 12);
    oled->ShowNumber(48, 12, HD2, 1, 12);

    oled->RefreshGram();
}

void oled_show(void)
{
    // 优化的显示函数 - 适配4行显示系统
    // 使用正确的y坐标：0, 16, 32, 48（每行16像素）

    // 降低显示更新频率，防止I2C阻塞
    static int display_counter = 0;
    if(++display_counter < 3) return;  // 每300ms更新一次
    display_counter = 0;

    //第一行：传感器状态和角度
    oled->ShowString(0,0,"HD:");
    oled->ShowNumber(24,0,HD2,1,12);
    oled->ShowNumber(32,0,HD1,1,12);
    oled->ShowNumber(40,0,HD0,1,12);
    oled->ShowString(56,0,"A:");
    oled->ShowFloat(72,0,mpu6050.yaw-m6050init,3,1);

    //第二行：方向和按键状态
    oled->ShowString(0,16,"fx:");
    oled->ShowNumber(24,16,fx,2,12);
    oled->ShowString(48,16,"K:");
    oled->ShowNumber(64,16,KEY0,1,12);
    oled->ShowString(80,16,"T:");
    oled->ShowNumber(96,16,time%1000,3,12);

    // 方向判断逻辑
    if(mpu6050.yaw>(m6050init-45) && mpu6050.yaw<(m6050init+45))fx=50;
    if(mpu6050.yaw>(m6050init+45) && mpu6050.yaw<(m6050init+135))fx=51;
    if((mpu6050.yaw>(m6050init+135) && mpu6050.yaw<(m6050init+180)) || (mpu6050.yaw>(m6050init-180) && mpu6050.yaw<(m6050init-135)) )fx=52;
    if(mpu6050.yaw>(m6050init-135) && mpu6050.yaw<(m6050init-45))fx=53;
    if(fx!=lfx)DL_UART_transmitData(UART_2_INST,fx),lfx=fx;
    if(time>1050)time=1000,DL_UART_transmitData(UART_2_INST,fx),lfx=fx;

    //第三行：左轮信息
    oled->ShowString(0,32,"L:");
    if( Motor_Left < 0 )  oled->ShowString(16,32,"-");
    else oled->ShowString(16,32,"+");
    oled->ShowNumber(24,32,abs(xuanti),3,12);
    oled->ShowString(48,32,"|");
    oled->ShowNumber(56,32,abs((int)Encoder_Left),4,12);

    //第四行：右轮信息和状态
    oled->ShowString(0,48,"R:");
    if( Velocity_Right < 0 )  oled->ShowString(16,48,"-");
    else oled->ShowString(16,48,"+");
    oled->ShowNumber(24,48,abs(gy),3,12);
    oled->ShowString(48,48,"|");
    oled->ShowFloat(56,48,robotVol,2,1);
    oled->ShowString(72,48,"V");

    if( Flag_Stop )
        oled->ShowString(88,48,"OFF");
    else
        oled->ShowString(88,48,"ON ");

    //刷新显示 - 添加异常检测
    static int refresh_error_count = 0;

    // 检测显示是否正常
    if(time > 0) {  // 确保系统正常运行
        oled->RefreshGram();
        refresh_error_count = 0;
    } else {
        refresh_error_count++;
        if(refresh_error_count > 10) {
            // 显示系统可能异常，跳过本次刷新
            refresh_error_count = 0;
            return;
        }
    }
}
